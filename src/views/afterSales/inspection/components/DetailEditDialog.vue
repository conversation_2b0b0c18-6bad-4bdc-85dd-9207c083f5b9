<script setup lang="ts">
import { computed } from 'vue';
import { ElDialog, ElDescriptions, ElDescriptionsItem } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { InspectionListItem } from '@/types/afterSales/inspection.d.ts';

interface Props {
  visible: boolean;
  recordData: InspectionListItem | null;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.inspection');

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const currentRecord = computed(() => props.recordData);

const getStatusText = (status: string) => {
  return t(`status.${status}`);
};

const getRegisterTypeText = (type: string) => {
  return type === 'appointment' ? t('registerType.appointment') : t('registerType.walk_in');
};

const getServiceTypeText = (type: string) => {
  return type === 'maintenance' ? t('serviceType.maintenance') : t('serviceType.repair');
};
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="t('dialog.detailTitle')"
    width="800px"
  >
    <div v-if="currentRecord">
      <el-descriptions :column="2" border>
        <el-descriptions-item :label="t('table.inspectionNo')">
          {{ currentRecord.inspectionNo }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.inspectionStatus')">
          {{ getStatusText(currentRecord.inspectionStatus) }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.repairmanName')">
          {{ currentRecord.repairmanName }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.repairmanPhone')">
          {{ currentRecord.repairmanPhone }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.licensePlateNo')">
          {{ currentRecord.licensePlateNo }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.vehicleModel')">
          {{ currentRecord.vehicleModel }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.vehicleConfig')">
          {{ currentRecord.vehicleConfig }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.color')">
          {{ currentRecord.color }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.mileage')">
          {{ currentRecord.mileage }} 公里
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.vehicleAge')">
          {{ currentRecord.vehicleAge }} 个月
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.serviceAdvisor')">
          {{ currentRecord.serviceAdvisor }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.technician')">
          {{ currentRecord.technician }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.registerType')">
          {{ getRegisterTypeText(currentRecord.registerType) }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.serviceType')">
          {{ getServiceTypeText(currentRecord.serviceType) }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.customerConfirmTime')">
          {{ currentRecord.customerConfirmTime || '-' }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.createTime')">
          {{ currentRecord.createTime }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.updateTime')">
          {{ currentRecord.updateTime }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">
// 详情弹窗样式，如果需要可以定制
</style>
