<template>
  <el-card class="table-card">
    <el-table
      :data="checkinList"
      :loading="loading"
      v-loading="loading"
      border
:empty-text="t('actions.noData', '暂无数据')"
      style="width: 100%"
    >
      <!-- 序号列 -->
      <el-table-column type="index" :label="t('id')" width="60" />

      <!-- 登记编号 -->
      <el-table-column prop="checkinId" :label="t('checkinId')" min-width="120" />

      <!-- 车牌号 -->
      <el-table-column prop="licensePlate" :label="t('licensePlate')" min-width="120" />

      <!-- VIN码 -->
      <el-table-column prop="vin" :label="t('vin')" min-width="140" />

      <!-- 车型 -->
      <el-table-column prop="vehicleModel" :label="t('vehicleModel')" min-width="120" />

      <!-- 车辆配置 -->
      <el-table-column prop="vehicleConfiguration" :label="t('vehicleConfiguration')" min-width="150" />

      <!-- 颜色 -->
      <el-table-column prop="color" :label="t('color')" min-width="80" />

      <!-- 里程数 -->
      <el-table-column :label="t('mileage')" min-width="100">
        <template #default="scope">
          {{ scope.row.mileage ? `${scope.row.mileage} ${t('mileageUnit')}` : '-' }}
        </template>
      </el-table-column>

      <!-- 车龄 -->
      <el-table-column :label="t('vehicleAge')" min-width="80">
        <template #default="scope">
          {{ scope.row.vehicleAge ? `${scope.row.vehicleAge} ${t('vehicleAgeUnit')}` : '-' }}
        </template>
      </el-table-column>

      <!-- 送修人姓名 -->
      <el-table-column prop="repairPersonName" :label="t('repairPersonName')" min-width="120" />

      <!-- 送修人电话 -->
      <el-table-column prop="repairPersonPhone" :label="t('repairPersonPhone')" min-width="120" />

      <!-- 服务顾问 -->
      <el-table-column prop="serviceAdvisor" :label="t('serviceAdvisor')" min-width="100" />

      <!-- 环检单号 -->
      <el-table-column :label="t('relatedRepairOrderId')" min-width="140">
        <template #default="scope">
          {{ scope.row.relatedRepairOrderId || '-' }}
        </template>
      </el-table-column>

      <!-- 服务类型 -->
      <el-table-column :label="t('serviceType')" min-width="100">
        <template #default="scope">
          {{ t('serviceTypeOptions.repair') }}
        </template>
      </el-table-column>

      <!-- 创建时间 -->
      <el-table-column :label="t('createdAt')" min-width="120">
        <template #default="scope">
          {{ formatDateTime(scope.row.createdAt) }}
        </template>
      </el-table-column>

      <!-- 更新时间 -->
      <el-table-column :label="t('updatedAt')" min-width="120">
        <template #default="scope">
          {{ formatDateTime(scope.row.updatedAt) }}
        </template>
      </el-table-column>

      <!-- 操作列 -->
      <el-table-column :label="t('actions.view', '操作')" width="280" fixed="right">
        <template #default="scope">
          <el-button
            type="primary"
            link
            :icon="View"
            @click="$emit('view-details', scope.row)"
          >
            {{ t('actions.view') }}
          </el-button>

          <el-button
            type="primary"
            link
            :icon="Edit"
            @click="$emit('edit-record', scope.row)"
          >
            {{ t('actions.edit') }}
          </el-button>

          <el-button
            v-if="scope.row.status === 'normal' && !scope.row.relatedRepairOrderId"
            type="danger"
            link
            :icon="Delete"
            @click="$emit('cancel-record', scope.row)"
          >
            {{ t('actions.cancel') }}
          </el-button>

          <el-button
            v-if="scope.row.status === 'normal' && !scope.row.relatedRepairOrderId"
            type="success"
            link
            :icon="Plus"
            @click="$emit('create-repair-order', scope.row)"
          >
            {{ t('actions.createRepairOrder') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="pagination.current"
        :page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @current-change="$emit('page-change', $event)"
        @size-change="$emit('page-size-change', $event)"
      />
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { View, Edit, Delete, Plus } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { CheckinItem } from '@/types/afterSales/checkin';

// 国际化
const { t } = useModuleI18n('afterSales.checkin');

interface Props {
  checkinList: CheckinItem[];
  loading: boolean;
  pagination: {
    current: number;
    size: number;
    total: number;
    pages: number;
  };
}

interface Emits {
  (e: 'view-details', row: CheckinItem): void;
  (e: 'edit-record', row: CheckinItem): void;
  (e: 'cancel-record', row: CheckinItem): void;
  (e: 'create-repair-order', row: CheckinItem): void;
  (e: 'page-change', page: number): void;
  (e: 'page-size-change', size: number): void;
}

defineProps<Props>();
defineEmits<Emits>();

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).replace(/\//g, '-');
};
</script>

<style lang="scss" scoped>
.table-card {
  margin-bottom: 20px;

  :deep(.el-table) {
    .el-table__body td,
    .el-table__header th {
      white-space: nowrap;
    }
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
