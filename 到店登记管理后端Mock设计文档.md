# 到店登记管理后端Mock设计文档

## 1. 文档概述

### 1.1 设计目标
基于到店登记管理API接口文档，遵循后端Mock服务开发规范V1.1，为前端开发提供完整的Mock数据支持，确保前后端并行开发的顺利进行。

### 1.2 核心原则
- **非侵入性**: Mock逻辑不污染业务代码
- **配置驱动**: 通过YAML配置控制Mock开关
- **契约一致**: Mock数据严格遵守API契约
- **逻辑分离**: Mock数据生成与业务逻辑完全分离

### 1.3 技术栈
- **框架**: Spring Boot
- **数据生成**: 实时按需生成
- **配置管理**: YAML配置文件
- **依赖注入**: Spring容器管理

## 2. 代码结构设计

### 2.1 包结构规范
```
src/main/java/com/perodua/bff/pc/
└── mock/
    ├── provider/
    │   └── MockDataProvider.java           # Mock数据提供者
    ├── util/
    │   └── MockDataGenerator.java          # Mock数据生成工具
    └── constants/
        └── CheckinMockConstants.java       # Mock常量定义
```

### 2.2 业务服务集成
```
src/main/java/com/perodua/bff/pc/service/impl/
└── CheckinServiceImpl.java                # 到店登记服务实现类
```

## 3. Mock数据结构设计

### 3.1 核心数据模型

#### 3.1.1 登记单VO对象
```java
@Data
@Schema(description = "到店登记信息")
public class CheckinVO {
    @Schema(description = "登记ID")
    private Long id;
    
    @Schema(description = "登记编号", example = "DH20240731001")
    private String checkinId;
    
    @Schema(description = "车牌号", example = "WKL1234A")
    private String licensePlate;
    
    @Schema(description = "VIN码", example = "WVWZZZ1JZ3W123456")
    private String vin;
    
    @Schema(description = "车型", example = "Proton X50")
    private String vehicleModel;
    
    @Schema(description = "车辆配置", example = "1.5T Premium")
    private String vehicleConfiguration;
    
    @Schema(description = "颜色", example = "Snow White")
    private String color;
    
    @Schema(description = "里程数", example = "25000")
    private Integer mileage;
    
    @Schema(description = "车龄（月）", example = "36")
    private Integer vehicleAge;
    
    @Schema(description = "送修人姓名", example = "Ahmad Ali")
    private String repairPersonName;
    
    @Schema(description = "送修人电话", example = "**********")
    private String repairPersonPhone;
    
    @Schema(description = "服务顾问", example = "Sarah Lim")
    private String serviceAdvisor;
    
    @Schema(description = "关联环检单编号", example = "ENV20240731001")
    private String relatedRepairOrderId;
    
    @Schema(description = "服务类型", example = "维修")
    private String serviceType;
    
    @Schema(description = "登记状态", example = "normal")
    private String status;
    
    @Schema(description = "备注")
    private String notes;
    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;
    
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;
}
```

#### 3.1.2 分页响应对象
```java
@Data
@Schema(description = "分页查询响应")
public class CheckinPageVO {
    @Schema(description = "当前页数据列表")
    private List<CheckinVO> records;
    
    @Schema(description = "总记录数")
    private Long total;
    
    @Schema(description = "每页显示条数")
    private Long size;
    
    @Schema(description = "当前页码")
    private Long current;
    
    @Schema(description = "总页数")
    private Long pages;
}
```

#### 3.1.3 车辆信息VO对象
```java
@Data
@Schema(description = "车辆信息")
public class VehicleInfoVO {
    @Schema(description = "车牌号", example = "WKL1234A")
    private String licensePlate;
    
    @Schema(description = "VIN码", example = "WVWZZZ1JZ3W123456")
    private String vin;
    
    @Schema(description = "车型", example = "Proton X50")
    private String vehicleModel;
    
    @Schema(description = "车辆配置", example = "1.5T Premium")
    private String vehicleConfiguration;
    
    @Schema(description = "颜色", example = "Snow White")
    private String color;
    
    @Schema(description = "车龄（月）", example = "36")
    private Integer vehicleAge;
}
```

### 3.2 Mock常量定义

#### 3.2.1 CheckinMockConstants.java
```java
package com.perodua.bff.pc.mock.constants;

import java.util.Arrays;
import java.util.List;

/**
 * 到店登记Mock数据常量
 */
public class CheckinMockConstants {
    
    // 马来西亚车牌号前缀
    public static final List<String> LICENSE_PLATE_PREFIXES = Arrays.asList(
        "WKL", "WML", "WNL", "WQL", "WSL", "WTL", "WUL", "WVL", "WWL", "WXL", // Kuala Lumpur
        "JHR", "JEA", "JEB", "JEC", "JED", "JEE", "JEF", "JEG", "JEH", "JEJ", // Johor
        "PNG", "PBA", "PBC", "PBD", "PBE", "PBF", "PBG", "PBH", "PBJ", "PBK", // Penang
        "SGR", "SBA", "SBC", "SBD", "SBE", "SBF", "SBG", "SBH", "SBJ", "SBK"  // Selangor
    );
    
    // Proton车型列表
    public static final List<String> PROTON_MODELS = Arrays.asList(
        "Proton X50", "Proton X70", "Proton Saga", "Proton Persona", 
        "Proton Exora", "Proton Iriz", "Proton Preve", "Proton Inspira"
    );
    
    // 车辆配置列表
    public static final List<String> VEHICLE_CONFIGURATIONS = Arrays.asList(
        "1.5T Standard", "1.5T Executive", "1.5T Premium", "1.5T Flagship",
        "1.3L Standard", "1.3L Executive", "1.3L Premium",
        "1.6L CVT Standard", "1.6L CVT Premium"
    );
    
    // 车辆颜色列表
    public static final List<String> VEHICLE_COLORS = Arrays.asList(
        "Snow White", "Jet Black", "Armor Silver", "Passion Red", 
        "Ocean Blue", "Granite Grey", "Citric Orange", "Ruby Red"
    );
    
    // 马来西亚常见姓名
    public static final List<String> MALAYSIAN_NAMES = Arrays.asList(
        "Ahmad Ali", "Siti Nurhaliza", "Muhammad Ibrahim", "Fatimah Zahra",
        "Raj Kumar", "Priya Devi", "Tan Wei Ming", "Lim Mei Ling",
        "Wong Ah Kow", "Lee Siew Hoon", "Chen Li Ming", "Ng Boon Huat",
        "Ravi Shankar", "Deepa Kumari", "Sarah Johnson", "David Smith"
    );
    
    // 服务顾问列表
    public static final List<String> SERVICE_ADVISORS = Arrays.asList(
        "Sarah Lim", "Ahmad Rahman", "Raj Patel", "Melissa Tan",
        "Kevin Wong", "Nurul Aina", "James Lee", "Priya Sharma"
    );
    
    // 登记状态
    public static final String STATUS_NORMAL = "normal";
    public static final String STATUS_CANCELLED = "cancelled";
    
    // 服务类型
    public static final String SERVICE_TYPE_REPAIR = "维修";
    
    // 门店简称列表
    public static final List<String> DEALER_CODES = Arrays.asList(
        "KL", "JB", "PG", "SG", "MLK", "NS", "PRK", "KDH", "TRG", "KEL", "PHG", "SBH", "SWK"
    );
}
```

## 4. Mock数据生成器设计

### 4.1 MockDataGenerator.java
```java
package com.perodua.bff.pc.mock.util;

import com.perodua.bff.pc.dto.CheckinVO;
import com.perodua.bff.pc.dto.VehicleInfoVO;
import com.perodua.bff.pc.mock.constants.CheckinMockConstants;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * Mock数据生成工具类
 */
@Component
public class MockDataGenerator {
    
    private static final Random RANDOM = new Random();
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 生成随机车牌号
     */
    public static String generateLicensePlate() {
        String prefix = CheckinMockConstants.LICENSE_PLATE_PREFIXES.get(
            RANDOM.nextInt(CheckinMockConstants.LICENSE_PLATE_PREFIXES.size())
        );
        int number = ThreadLocalRandom.current().nextInt(1000, 9999);
        char letter = (char) ('A' + RANDOM.nextInt(26));
        return prefix + number + letter;
    }
    
    /**
     * 生成随机VIN码
     */
    public static String generateVIN() {
        StringBuilder vin = new StringBuilder("WVWZZZ");
        String chars = "ABCDEFGHJKLMNPRSTUVWXYZ**********";
        for (int i = 0; i < 11; i++) {
            vin.append(chars.charAt(RANDOM.nextInt(chars.length())));
        }
        return vin.toString();
    }
    
    /**
     * 生成随机手机号（马来西亚格式）
     */
    public static String generatePhoneNumber() {
        String[] prefixes = {"012", "013", "014", "016", "017", "018", "019"};
        String prefix = prefixes[RANDOM.nextInt(prefixes.length)];
        int number = ThreadLocalRandom.current().nextInt(1000000, 9999999);
        return prefix + number;
    }
    
    /**
     * 生成登记编号
     */
    public static String generateCheckinId() {
        String dealerCode = CheckinMockConstants.DEALER_CODES.get(
            RANDOM.nextInt(CheckinMockConstants.DEALER_CODES.size())
        );
        String date = new SimpleDateFormat("yyyyMMdd").format(new Date());
        int sequence = ThreadLocalRandom.current().nextInt(1, 999);
        return dealerCode + date + String.format("%03d", sequence);
    }
    
    /**
     * 生成环检单编号
     */
    public static String generateRepairOrderId() {
        String date = new SimpleDateFormat("yyyyMMdd").format(new Date());
        int sequence = ThreadLocalRandom.current().nextInt(1, 999);
        return "ENV" + date + String.format("%03d", sequence);
    }
    
    /**
     * 生成随机日期（最近30天内）
     */
    public static Date generateRecentDate() {
        long now = System.currentTimeMillis();
        long thirtyDaysAgo = now - (30L * 24 * 60 * 60 * 1000);
        long randomTime = ThreadLocalRandom.current().nextLong(thirtyDaysAgo, now);
        return new Date(randomTime);
    }
    
    /**
     * 生成完整的登记单Mock数据
     */
    public static CheckinVO generateCheckinVO(Long id) {
        CheckinVO checkin = new CheckinVO();
        checkin.setId(id);
        checkin.setCheckinId(generateCheckinId());
        checkin.setLicensePlate(generateLicensePlate());
        checkin.setVin(generateVIN());
        
        // 车辆信息
        checkin.setVehicleModel(CheckinMockConstants.PROTON_MODELS.get(
            RANDOM.nextInt(CheckinMockConstants.PROTON_MODELS.size())
        ));
        checkin.setVehicleConfiguration(CheckinMockConstants.VEHICLE_CONFIGURATIONS.get(
            RANDOM.nextInt(CheckinMockConstants.VEHICLE_CONFIGURATIONS.size())
        ));
        checkin.setColor(CheckinMockConstants.VEHICLE_COLORS.get(
            RANDOM.nextInt(CheckinMockConstants.VEHICLE_COLORS.size())
        ));
        checkin.setMileage(ThreadLocalRandom.current().nextInt(1000, 150000));
        checkin.setVehicleAge(ThreadLocalRandom.current().nextInt(1, 120)); // 1-120个月
        
        // 客户信息
        checkin.setRepairPersonName(CheckinMockConstants.MALAYSIAN_NAMES.get(
            RANDOM.nextInt(CheckinMockConstants.MALAYSIAN_NAMES.size())
        ));
        checkin.setRepairPersonPhone(generatePhoneNumber());
        checkin.setServiceAdvisor(CheckinMockConstants.SERVICE_ADVISORS.get(
            RANDOM.nextInt(CheckinMockConstants.SERVICE_ADVISORS.size())
        ));
        
        // 业务信息
        checkin.setServiceType(CheckinMockConstants.SERVICE_TYPE_REPAIR);
        
        // 状态信息 - 70%概率为正常状态
        boolean isNormal = RANDOM.nextDouble() < 0.7;
        checkin.setStatus(isNormal ? CheckinMockConstants.STATUS_NORMAL : CheckinMockConstants.STATUS_CANCELLED);
        
        // 关联环检单 - 正常状态下50%概率有关联
        if (isNormal && RANDOM.nextBoolean()) {
            checkin.setRelatedRepairOrderId(generateRepairOrderId());
        }
        
        // 备注信息 - 30%概率有备注
        if (RANDOM.nextDouble() < 0.3) {
            checkin.setNotes("客户反映：" + getRandomNote());
        }
        
        // 时间信息
        Date createdAt = generateRecentDate();
        checkin.setCreatedAt(createdAt);
        checkin.setUpdatedAt(new Date(createdAt.getTime() + ThreadLocalRandom.current().nextLong(0, 24 * 60 * 60 * 1000)));
        
        return checkin;
    }
    
    /**
     * 生成车辆信息Mock数据
     */
    public static VehicleInfoVO generateVehicleInfoVO(String licensePlate) {
        VehicleInfoVO vehicleInfo = new VehicleInfoVO();
        vehicleInfo.setLicensePlate(licensePlate);
        vehicleInfo.setVin(generateVIN());
        vehicleInfo.setVehicleModel(CheckinMockConstants.PROTON_MODELS.get(
            RANDOM.nextInt(CheckinMockConstants.PROTON_MODELS.size())
        ));
        vehicleInfo.setVehicleConfiguration(CheckinMockConstants.VEHICLE_CONFIGURATIONS.get(
            RANDOM.nextInt(CheckinMockConstants.VEHICLE_CONFIGURATIONS.size())
        ));
        vehicleInfo.setColor(CheckinMockConstants.VEHICLE_COLORS.get(
            RANDOM.nextInt(CheckinMockConstants.VEHICLE_COLORS.size())
        ));
        vehicleInfo.setVehicleAge(ThreadLocalRandom.current().nextInt(1, 120));
        
        return vehicleInfo;
    }
    
    /**
     * 获取随机备注内容
     */
    private static String getRandomNote() {
        String[] notes = {
            "发动机异响需要检查",
            "刹车片可能需要更换",
            "空调制冷效果不佳",
            "轮胎磨损严重",
            "电池电量不足",
            "转向异响",
            "怠速不稳定",
            "油耗偏高需要检测"
        };
        return notes[RANDOM.nextInt(notes.length)];
    }
}
```

## 5. Mock数据提供者设计

### 5.1 MockDataProvider.java
```java
package com.perodua.bff.pc.mock.provider;

import com.perodua.bff.pc.dto.*;
import com.perodua.bff.pc.mock.util.MockDataGenerator;
import com.perodua.bff.pc.request.CheckinPageQuery;
import org.springframework.stereotype.Component;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * 到店登记Mock数据提供者
 */
@Component
public class MockDataProvider {
    
    // 模拟数据存储（实际项目中可以使用内存数据库如H2）
    private static final Map<Long, CheckinVO> MOCK_DATA_STORE = new HashMap<>();
    private static Long ID_GENERATOR = 1L;
    
    // 初始化模拟数据
    static {
        for (int i = 0; i < 50; i++) {
            CheckinVO checkin = MockDataGenerator.generateCheckinVO(ID_GENERATOR++);
            MOCK_DATA_STORE.put(checkin.getId(), checkin);
        }
    }
    
    /**
     * 获取分页登记列表Mock数据
     */
    public CheckinPageVO getMockCheckinPage(CheckinPageQuery query) {
        List<CheckinVO> allData = new ArrayList<>(MOCK_DATA_STORE.values());
        
        // 应用筛选条件
        List<CheckinVO> filteredData = allData.stream()
            .filter(item -> matchesFilter(item, query))
            .sorted((a, b) -> b.getCreatedAt().compareTo(a.getCreatedAt())) // 按创建时间倒序
            .collect(Collectors.toList());
        
        // 分页处理
        long pageNum = query.getPageNum() != null ? query.getPageNum() : 1L;
        long pageSize = query.getPageSize() != null ? query.getPageSize() : 10L;
        long total = filteredData.size();
        long pages = (total + pageSize - 1) / pageSize;
        
        int startIndex = (int) ((pageNum - 1) * pageSize);
        int endIndex = Math.min(startIndex + (int) pageSize, filteredData.size());
        
        List<CheckinVO> pageData = startIndex < filteredData.size() ? 
            filteredData.subList(startIndex, endIndex) : new ArrayList<>();
        
        CheckinPageVO pageResult = new CheckinPageVO();
        pageResult.setRecords(pageData);
        pageResult.setTotal(total);
        pageResult.setSize(pageSize);
        pageResult.setCurrent(pageNum);
        pageResult.setPages(pages);
        
        return pageResult;
    }
    
    /**
     * 根据ID获取登记详情Mock数据
     */
    public CheckinVO getMockCheckinDetail(Long id) {
        CheckinVO checkin = MOCK_DATA_STORE.get(id);
        if (checkin == null) {
            // 如果不存在，动态生成一个
            checkin = MockDataGenerator.generateCheckinVO(id);
            MOCK_DATA_STORE.put(id, checkin);
        }
        return checkin;
    }
    
    /**
     * 根据车牌号查询车辆信息Mock数据
     */
    public VehicleInfoVO getMockVehicleInfo(String licensePlate) {
        // 模拟70%的查询成功率
        if (ThreadLocalRandom.current().nextDouble() > 0.7) {
            return null; // 模拟查询不到车辆信息
        }
        return MockDataGenerator.generateVehicleInfoVO(licensePlate);
    }
    
    /**
     * 根据车主IC查询车辆信息Mock数据
     */
    public VehicleInfoVO getMockVehicleInfoByOwnerIC(String ownerIC) {
        // 模拟60%的查询成功率
        if (ThreadLocalRandom.current().nextDouble() > 0.6) {
            return null; // 模拟查询不到车辆信息
        }
        // 生成一个随机车牌号的车辆信息
        String randomLicensePlate = MockDataGenerator.generateLicensePlate();
        return MockDataGenerator.generateVehicleInfoVO(randomLicensePlate);
    }
    
    /**
     * 创建登记Mock数据
     */
    public CheckinCreateVO getMockCreateCheckin(CheckinCreateForm form) {
        Long newId = ID_GENERATOR++;
        String checkinId = MockDataGenerator.generateCheckinId();
        
        // 构建完整的登记对象并存储
        CheckinVO checkin = new CheckinVO();
        checkin.setId(newId);
        checkin.setCheckinId(checkinId);
        checkin.setLicensePlate(form.getLicensePlate());
        checkin.setVin(form.getVin());
        checkin.setVehicleModel(form.getVehicleModel());
        checkin.setVehicleConfiguration(form.getVehicleConfiguration());
        checkin.setColor(form.getColor());
        checkin.setMileage(form.getMileage());
        checkin.setRepairPersonName(form.getRepairPersonName());
        checkin.setRepairPersonPhone(form.getRepairPersonPhone());
        checkin.setServiceAdvisor(form.getServiceAdvisor());
        checkin.setServiceType(form.getServiceType());
        checkin.setNotes(form.getNotes());
        checkin.setStatus("normal");
        checkin.setCreatedAt(new Date());
        checkin.setUpdatedAt(new Date());
        
        // 计算车龄（如果有VIN码的话，这里简单随机生成）
        if (form.getVin() != null && !form.getVin().isEmpty()) {
            checkin.setVehicleAge(ThreadLocalRandom.current().nextInt(1, 120));
        }
        
        MOCK_DATA_STORE.put(newId, checkin);
        
        CheckinCreateVO result = new CheckinCreateVO();
        result.setId(newId);
        result.setCheckinId(checkinId);
        return result;
    }
    
    /**
     * 更新登记Mock数据
     */
    public void getMockUpdateCheckin(Long id, CheckinUpdateForm form) {
        CheckinVO existing = MOCK_DATA_STORE.get(id);
        if (existing != null) {
            // 更新可编辑字段
            if (form.getMileage() != null) {
                existing.setMileage(form.getMileage());
            }
            if (form.getRepairPersonName() != null) {
                existing.setRepairPersonName(form.getRepairPersonName());
            }
            if (form.getRepairPersonPhone() != null) {
                existing.setRepairPersonPhone(form.getRepairPersonPhone());
            }
            if (form.getServiceAdvisor() != null) {
                existing.setServiceAdvisor(form.getServiceAdvisor());
            }
            if (form.getNotes() != null) {
                existing.setNotes(form.getNotes());
            }
            existing.setUpdatedAt(new Date());
        }
    }
    
    /**
     * 取消登记Mock数据
     */
    public void getMockCancelCheckin(Long id, String reason) {
        CheckinVO existing = MOCK_DATA_STORE.get(id);
        if (existing != null && "normal".equals(existing.getStatus()) && existing.getRelatedRepairOrderId() == null) {
            existing.setStatus("cancelled");
            existing.setNotes(existing.getNotes() + "\n取消原因：" + reason);
            existing.setUpdatedAt(new Date());
        }
    }
    
    /**
     * 创建环检单Mock数据
     */
    public RepairOrderCreateVO getMockCreateRepairOrder(Long checkinId) {
        CheckinVO existing = MOCK_DATA_STORE.get(checkinId);
        if (existing != null && "normal".equals(existing.getStatus()) && existing.getRelatedRepairOrderId() == null) {
            String repairOrderCode = MockDataGenerator.generateRepairOrderId();
            existing.setRelatedRepairOrderId(repairOrderCode);
            existing.setUpdatedAt(new Date());
            
            RepairOrderCreateVO result = new RepairOrderCreateVO();
            result.setRepairOrderId(ThreadLocalRandom.current().nextLong(1000, 9999));
            result.setRepairOrderCode(repairOrderCode);
            return result;
        }
        return null;
    }
    
    /**
     * 筛选条件匹配
     */
    private boolean matchesFilter(CheckinVO item, CheckinPageQuery query) {
        if (query.getCheckinId() != null && !query.getCheckinId().isEmpty()) {
            if (!item.getCheckinId().toLowerCase().contains(query.getCheckinId().toLowerCase())) {
                return false;
            }
        }
        
        if (query.getLicensePlate() != null && !query.getLicensePlate().isEmpty()) {
            if (!item.getLicensePlate().toLowerCase().contains(query.getLicensePlate().toLowerCase())) {
                return false;
            }
        }
        
        if (query.getRepairPersonName() != null && !query.getRepairPersonName().isEmpty()) {
            if (!item.getRepairPersonName().toLowerCase().contains(query.getRepairPersonName().toLowerCase())) {
                return false;
            }
        }
        
        if (query.getRepairPersonPhone() != null && !query.getRepairPersonPhone().isEmpty()) {
            if (!item.getRepairPersonPhone().contains(query.getRepairPersonPhone())) {
                return false;
            }
        }
        
        if (query.getStatus() != null && !query.getStatus().isEmpty() && !"all".equals(query.getStatus())) {
            if (!item.getStatus().equals(query.getStatus())) {
                return false;
            }
        }
        
        // 日期范围筛选
        if (query.getCreatedAtStart() != null) {
            if (item.getCreatedAt().before(query.getCreatedAtStart())) {
                return false;
            }
        }
        
        if (query.getCreatedAtEnd() != null) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(query.getCreatedAtEnd());
            cal.add(Calendar.DAY_OF_MONTH, 1); // 结束日期包含当天
            if (item.getCreatedAt().after(cal.getTime())) {
                return false;
            }
        }
        
        return true;
    }
}
```

## 6. 服务层集成设计

### 6.1 CheckinServiceImpl.java
```java
package com.perodua.bff.pc.service.impl;

import com.perodua.bff.pc.dto.*;
import com.perodua.bff.pc.mock.provider.MockDataProvider;
import com.perodua.bff.pc.request.*;
import com.perodua.bff.pc.service.CheckinService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 到店登记服务实现类
 */
@Slf4j
@Service
public class CheckinServiceImpl implements CheckinService {
    
    @Autowired
    private MockDataProvider mockDataProvider;
    
    // Mock开关配置
    @Value("${mock.feature.checkin.list.enabled:false}")
    private boolean mockCheckinListEnabled;
    
    @Value("${mock.feature.checkin.detail.enabled:false}")
    private boolean mockCheckinDetailEnabled;
    
    @Value("${mock.feature.checkin.vehicle-info.enabled:false}")
    private boolean mockVehicleInfoEnabled;
    
    @Value("${mock.feature.checkin.create.enabled:false}")
    private boolean mockCheckinCreateEnabled;
    
    @Value("${mock.feature.checkin.update.enabled:false}")
    private boolean mockCheckinUpdateEnabled;
    
    @Value("${mock.feature.checkin.cancel.enabled:false}")
    private boolean mockCheckinCancelEnabled;
    
    @Value("${mock.feature.checkin.repair-order.enabled:false}")
    private boolean mockRepairOrderCreateEnabled;
    
    @Override
    public CheckinPageVO getCheckinList(CheckinPageQuery query) {
        if (mockCheckinListEnabled) {
            log.info("Using mock data for checkin list query: {}", query);
            return mockDataProvider.getMockCheckinPage(query);
        }
        
        // 真实业务逻辑
        // TODO: 调用真实的下游服务
        throw new UnsupportedOperationException("Real implementation not ready yet");
    }
    
    @Override
    public CheckinVO getCheckinDetail(Long id) {
        if (mockCheckinDetailEnabled) {
            log.info("Using mock data for checkin detail: {}", id);
            return mockDataProvider.getMockCheckinDetail(id);
        }
        
        // 真实业务逻辑
        // TODO: 调用真实的下游服务
        throw new UnsupportedOperationException("Real implementation not ready yet");
    }
    
    @Override
    public VehicleInfoVO getVehicleInfoByLicensePlate(String licensePlate) {
        if (mockVehicleInfoEnabled) {
            log.info("Using mock data for vehicle info by license plate: {}", licensePlate);
            return mockDataProvider.getMockVehicleInfo(licensePlate);
        }
        
        // 真实业务逻辑
        // TODO: 调用真实的车辆信息服务
        throw new UnsupportedOperationException("Real implementation not ready yet");
    }
    
    @Override
    public VehicleInfoVO getVehicleInfoByOwnerIC(String ownerIC) {
        if (mockVehicleInfoEnabled) {
            log.info("Using mock data for vehicle info by owner IC: {}", ownerIC);
            return mockDataProvider.getMockVehicleInfoByOwnerIC(ownerIC);
        }
        
        // 真实业务逻辑
        // TODO: 调用真实的车辆信息服务
        throw new UnsupportedOperationException("Real implementation not ready yet");
    }
    
    @Override
    public CheckinCreateVO createCheckin(CheckinCreateForm form) {
        if (mockCheckinCreateEnabled) {
            log.info("Using mock data for create checkin: {}", form);
            return mockDataProvider.getMockCreateCheckin(form);
        }
        
        // 真实业务逻辑
        // TODO: 调用真实的下游服务
        throw new UnsupportedOperationException("Real implementation not ready yet");
    }
    
    @Override
    public void updateCheckin(Long id, CheckinUpdateForm form) {
        if (mockCheckinUpdateEnabled) {
            log.info("Using mock data for update checkin: {} with form: {}", id, form);
            mockDataProvider.getMockUpdateCheckin(id, form);
            return;
        }
        
        // 真实业务逻辑
        // TODO: 调用真实的下游服务
        throw new UnsupportedOperationException("Real implementation not ready yet");
    }
    
    @Override
    public void cancelCheckin(Long id, String reason) {
        if (mockCheckinCancelEnabled) {
            log.info("Using mock data for cancel checkin: {} with reason: {}", id, reason);
            mockDataProvider.getMockCancelCheckin(id, reason);
            return;
        }
        
        // 真实业务逻辑
        // TODO: 调用真实的下游服务
        throw new UnsupportedOperationException("Real implementation not ready yet");
    }
    
    @Override
    public RepairOrderCreateVO createRepairOrder(Long checkinId) {
        if (mockRepairOrderCreateEnabled) {
            log.info("Using mock data for create repair order: {}", checkinId);
            return mockDataProvider.getMockCreateRepairOrder(checkinId);
        }
        
        // 真实业务逻辑
        // TODO: 调用真实的环检单服务
        throw new UnsupportedOperationException("Real implementation not ready yet");
    }
}
```

## 7. 配置文件设计

### 7.1 bootstrap-dev.yml
```yaml
# 到店登记管理Mock配置
mock:
  feature:
    checkin: # 到店登记模块
      list:
        enabled: true # 登记列表查询Mock开关
      detail:
        enabled: true # 登记详情查询Mock开关
      vehicle-info:
        enabled: true # 车辆信息查询Mock开关
      create:
        enabled: true # 新增登记Mock开关
      update:
        enabled: true # 编辑登记Mock开关
      cancel:
        enabled: true # 取消登记Mock开关
      repair-order:
        enabled: true # 创建环检单Mock开关

# 日志配置
logging:
  level:
    com.perodua.bff.pc.service.impl.CheckinServiceImpl: DEBUG
    com.perodua.bff.pc.mock: DEBUG
```

### 7.2 bootstrap-prod.yml
```yaml
# 生产环境 - 所有Mock功能关闭
mock:
  feature:
    checkin:
      list:
        enabled: false
      detail:
        enabled: false
      vehicle-info:
        enabled: false
      create:
        enabled: false
      update:
        enabled: false
      cancel:
        enabled: false
      repair-order:
        enabled: false
```

## 8. API控制器适配

### 8.1 CheckinController.java
```java
package com.perodua.bff.pc.controller;

import com.perodua.bff.pc.dto.*;
import com.perodua.bff.pc.request.*;
import com.perodua.bff.pc.service.CheckinService;
import com.perodua.bff.pc.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 到店登记管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/afterSales/checkins")
@Tag(name = "到店登记管理", description = "到店登记相关接口")
@Validated
public class CheckinController {
    
    @Autowired
    private CheckinService checkinService;
    
    @GetMapping
    @Operation(summary = "分页查询到店登记列表")
    public Result<CheckinPageVO> getCheckinList(@Valid CheckinPageQuery query) {
        log.info("Query checkin list with params: {}", query);
        CheckinPageVO result = checkinService.getCheckinList(query);
        return Result.success(result);
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "查询登记详情")
    public Result<CheckinVO> getCheckinDetail(
            @Parameter(description = "登记ID") @PathVariable @NotNull Long id) {
        log.info("Query checkin detail for id: {}", id);
        CheckinVO result = checkinService.getCheckinDetail(id);
        return Result.success(result);
    }
    
    @GetMapping("/vehicle-info")
    @Operation(summary = "根据车牌号或车主IC查询车辆信息")
    public Result<VehicleInfoVO> getVehicleInfo(
            @Parameter(description = "车牌号") @RequestParam(required = false) String licensePlate,
            @Parameter(description = "车主IC") @RequestParam(required = false) String ownerIC) {
        log.info("Query vehicle info with licensePlate: {}, ownerIC: {}", licensePlate, ownerIC);
        
        VehicleInfoVO result = null;
        if (licensePlate != null && !licensePlate.trim().isEmpty()) {
            result = checkinService.getVehicleInfoByLicensePlate(licensePlate.trim());
        } else if (ownerIC != null && !ownerIC.trim().isEmpty()) {
            result = checkinService.getVehicleInfoByOwnerIC(ownerIC.trim());
        }
        
        if (result != null) {
            return Result.success(result);
        } else {
            return Result.error("未查询到车辆信息");
        }
    }
    
    @PostMapping
    @Operation(summary = "新增到店登记")
    public Result<CheckinCreateVO> createCheckin(@Valid @RequestBody CheckinCreateForm form) {
        log.info("Create checkin with form: {}", form);
        CheckinCreateVO result = checkinService.createCheckin(form);
        return Result.success(result);
    }
    
    @PutMapping("/{id}")
    @Operation(summary = "编辑到店登记")
    public Result<Void> updateCheckin(
            @Parameter(description = "登记ID") @PathVariable @NotNull Long id,
            @Valid @RequestBody CheckinUpdateForm form) {
        log.info("Update checkin {} with form: {}", id, form);
        checkinService.updateCheckin(id, form);
        return Result.success();
    }
    
    @PostMapping("/{id}/cancel")
    @Operation(summary = "取消到店登记")
    public Result<Void> cancelCheckin(
            @Parameter(description = "登记ID") @PathVariable @NotNull Long id,
            @RequestBody @Valid CancelCheckinForm form) {
        log.info("Cancel checkin {} with reason: {}", id, form.getReason());
        checkinService.cancelCheckin(id, form.getReason());
        return Result.success();
    }
    
    @PostMapping("/{id}/repair-order")
    @Operation(summary = "创建环检单")
    public Result<RepairOrderCreateVO> createRepairOrder(
            @Parameter(description = "登记ID") @PathVariable @NotNull Long id) {
        log.info("Create repair order for checkin: {}", id);
        RepairOrderCreateVO result = checkinService.createRepairOrder(id);
        return Result.success(result);
    }
    
    @GetMapping("/export")
    @Operation(summary = "导出登记列表")
    public ResponseEntity<byte[]> exportCheckinList(@Valid CheckinExportQuery query) {
        log.info("Export checkin list with params: {}", query);
        
        // Mock导出功能 - 返回示例Excel文件
        String fileName = "checkin-list-" + System.currentTimeMillis() + ".xlsx";
        byte[] mockExcelData = generateMockExcelData();
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", fileName);
        
        return ResponseEntity.ok()
                .headers(headers)
                .body(mockExcelData);
    }
    
    /**
     * 生成Mock Excel数据
     */
    private byte[] generateMockExcelData() {
        // 简单的Mock Excel内容
        String mockContent = "登记编号,车牌号,车型,送修人,联系电话,创建时间\n" +
                "DH20240731001,WKL1234A,Proton X50,Ahmad Ali,**********,2024-07-31 10:00:00\n" +
                "DH20240731002,JHR5678B,Proton X70,Siti Nurhaliza,0198765432,2024-07-31 11:00:00";
        return mockContent.getBytes();
    }
}
```

## 9. 数据质量保证

### 9.1 Mock数据特点
- **真实性**: 使用马来西亚本地化的车牌号、姓名、电话格式
- **多样性**: 支持多种车型、配置、颜色组合
- **完整性**: 所有必填字段都有合理的默认值
- **一致性**: 数据关联关系符合业务逻辑

### 9.2 边界情况覆盖
- 正常登记单与已取消登记单的比例（7:3）
- 有环检单关联与无关联的比例（5:5）
- 车辆信息查询成功率（70%车牌号，60%车主IC）
- 备注信息覆盖率（30%）

### 9.3 性能考虑
- 使用内存Map存储，支持快速查询
- 分页查询时先筛选后分页，避免不必要的数据处理
- 日期比较使用高效的Date对象操作

## 10. 测试验证方案

### 10.1 功能测试用例

#### 10.1.1 列表查询测试
```bash
# 基础分页查询
curl -X GET "http://localhost:8080/api/v1/afterSales/checkins?pageNum=1&pageSize=10"

# 带条件筛选查询
curl -X GET "http://localhost:8080/api/v1/afterSales/checkins?licensePlate=WKL&status=normal"

# 日期范围查询
curl -X GET "http://localhost:8080/api/v1/afterSales/checkins?createdAtStart=2024-07-01&createdAtEnd=2024-07-31"
```

#### 10.1.2 车辆信息查询测试
```bash
# 车牌号查询
curl -X GET "http://localhost:8080/api/v1/afterSales/checkins/vehicle-info?licensePlate=WKL1234A"

# 车主IC查询
curl -X GET "http://localhost:8080/api/v1/afterSales/checkins/vehicle-info?ownerIC=123456789012"
```

#### 10.1.3 CRUD操作测试
```bash
# 新增登记
curl -X POST "http://localhost:8080/api/v1/afterSales/checkins" \
  -H "Content-Type: application/json" \
  -d '{"licensePlate":"WKL1234A","repairPersonName":"Test User","repairPersonPhone":"**********","serviceAdvisor":"Test Advisor","serviceType":"维修"}'

# 编辑登记
curl -X PUT "http://localhost:8080/api/v1/afterSales/checkins/1" \
  -H "Content-Type: application/json" \
  -d '{"mileage":30000,"notes":"Updated notes"}'

# 取消登记
curl -X POST "http://localhost:8080/api/v1/afterSales/checkins/1/cancel" \
  -H "Content-Type: application/json" \
  -d '{"reason":"客户取消预约"}'
```

### 10.2 配置切换测试
```yaml
# 测试Mock开关生效
mock:
  feature:
    checkin:
      list:
        enabled: false # 修改此配置并重启，验证是否抛出UnsupportedOperationException
```

### 10.3 性能测试
- 并发查询测试：50个并发请求，响应时间<500ms
- 大数据量测试：1000条数据分页查询，响应时间<1s
- 内存使用测试：长时间运行内存稳定

## 11. 部署和维护

### 11.1 环境配置清单
- **开发环境**: 所有Mock开关开启
- **测试环境**: 根据测试需要选择性开启
- **预生产环境**: 所有Mock开关关闭
- **生产环境**: 所有Mock开关关闭

### 11.2 监控和日志
```java
// 关键操作日志
log.info("Using mock data for checkin list query: {}", query);
log.debug("Mock data generated: {} records", result.getRecords().size());
log.warn("Mock vehicle info query failed for: {}", licensePlate);
```

### 11.3 清理策略
当真实接口开发完成后，按以下步骤清理Mock代码：
1. 关闭所有Mock配置开关
2. 移除服务实现类中的Mock判断逻辑
3. 移除MockDataProvider的注入依赖
4. 保留Mock相关类（便于后续测试使用）

## 12. 扩展性设计

### 12.1 新增Mock接口流程
1. 在MockDataProvider中添加新的Mock方法
2. 在服务实现类中添加Mock开关和判断逻辑
3. 在配置文件中添加对应的Mock开关
4. 更新常量类和生成器（如需要）

### 12.2 Mock数据持久化
如需要持久化Mock数据，可以：
1. 集成H2内存数据库
2. 使用Redis缓存存储
3. 实现数据导入导出功能

### 12.3 Mock数据管理界面
可以开发简单的管理界面用于：
1. 实时查看Mock数据
2. 动态调整Mock开关
3. 重置Mock数据

---

## 附录

### A. Mock开关配置清单
| 开关配置 | 功能说明 | 默认值 |
|---------|----------|--------|
| mock.feature.checkin.list.enabled | 登记列表查询 | false |
| mock.feature.checkin.detail.enabled | 登记详情查询 | false |
| mock.feature.checkin.vehicle-info.enabled | 车辆信息查询 | false |
| mock.feature.checkin.create.enabled | 新增登记 | false |
| mock.feature.checkin.update.enabled | 编辑登记 | false |
| mock.feature.checkin.cancel.enabled | 取消登记 | false |
| mock.feature.checkin.repair-order.enabled | 创建环检单 | false |

### B. Mock数据示例
```json
{
  "id": 1,
  "checkinId": "KL20240731001",
  "licensePlate": "WKL1234A",
  "vin": "WVWZZZ1JZ3W123456",
  "vehicleModel": "Proton X50",
  "vehicleConfiguration": "1.5T Premium",
  "color": "Snow White",
  "mileage": 25000,
  "vehicleAge": 36,
  "repairPersonName": "Ahmad Ali",
  "repairPersonPhone": "**********",
  "serviceAdvisor": "Sarah Lim",
  "relatedRepairOrderId": null,
  "serviceType": "维修",
  "status": "normal",
  "notes": "客户反映：发动机异响需要检查",
  "createdAt": "2024-07-31 10:00:00",
  "updatedAt": "2024-07-31 10:00:00"
}
```

### C. 常见问题排查
1. **Mock数据不生效**: 检查配置文件中的enabled开关
2. **数据格式不匹配**: 检查DTO对象字段映射
3. **查询条件不生效**: 检查MockDataProvider中的筛选逻辑
4. **并发问题**: 注意线程安全，考虑使用ConcurrentHashMap

---

**文档版本**: v1.0  
**创建日期**: 2025-07-31  
**维护团队**: 后端开发团队  
**审核状态**: 待审核