<script setup lang="ts">
import { ElCard, ElForm, ElFormItem, ElInput, ElDatePicker, ElButton, ElRow, ElCol } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { CheckinListParams } from '@/types/afterSales/checkin.d.ts';

interface Props {
  searchParams: CheckinListParams;
  dateRange: [string, string] | null;
}

interface Emits {
  (e: 'update:searchParams', value: CheckinListParams): void;
  (e: 'update:dateRange', value: [string, string] | null): void;
  (e: 'search'): void;
  (e: 'reset'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.checkin');

const handleSearch = () => {
  emit('search');
};

const handleReset = () => {
  emit('reset');
};

const updateSearchParams = (field: keyof CheckinListParams, value: any) => {
  emit('update:searchParams', { ...props.searchParams, [field]: value });
};

const updateDateRange = (value: [string, string] | null) => {
  emit('update:dateRange', value);
};
</script>

<template>
  <el-card class="mb-20 search-card">
    <el-form :model="searchParams" class="search-form" label-position="top">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="t('checkinId')">
            <el-input
              :model-value="searchParams.checkinId"
              @update:model-value="(val) => updateSearchParams('checkinId', val)"
              :placeholder="t('checkinIdPlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('licensePlate')">
            <el-input
              :model-value="searchParams.licensePlate"
              @update:model-value="(val) => updateSearchParams('licensePlate', val)"
              :placeholder="t('licensePlatePlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('repairPersonName')">
            <el-input
              :model-value="searchParams.repairPersonName"
              @update:model-value="(val) => updateSearchParams('repairPersonName', val)"
              :placeholder="t('repairPersonNamePlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('repairPersonPhone')">
            <el-input
              :model-value="searchParams.repairPersonPhone"
              @update:model-value="(val) => updateSearchParams('repairPersonPhone', val)"
              :placeholder="t('repairPersonPhonePlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="t('createdAt')">
            <el-date-picker
              :model-value="dateRange"
              @update:model-value="updateDateRange"
              type="daterange"
              range-separator="-"
              :start-placeholder="tc('startDate')"
              :end-placeholder="tc('endDate')"
              value-format="YYYY-MM-DD"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="18"></el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24" class="buttons-col">
          <el-form-item class="search-buttons">
            <el-button type="primary" :icon="Search" @click="handleSearch">{{ tc('search') }}</el-button>
            <el-button @click="handleReset">{{ tc('reset') }}</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<style scoped lang="scss">
.search-form {
  .el-form-item {
    margin-right: 20px;
    margin-bottom: 15px;
    &:last-child {
      margin-right: 0;
    }
  }
  .search-buttons {
    margin-bottom: 15px;
    .el-button {
      margin-left: 10px;
    }
  }
}

.buttons-col {
  text-align: right;
  :deep(.el-form-item__content) {
    justify-content: flex-end;
  }
}
</style>
