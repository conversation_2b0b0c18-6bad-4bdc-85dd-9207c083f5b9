<template>
  <el-dialog
    v-model="dialogVisible"
:title="isEdit ? t('dialog.editTitle') : t('dialog.addTitle')"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-position="top"
    >
      <!-- 车牌号查询区 -->
      <el-row :gutter="20">
        <el-col :span="18">
          <el-form-item :label="t('vehicleQuery')">
            <el-input
              v-model="queryLicensePlate"
              :placeholder="t('vehicleQueryPlaceholder')"
              :disabled="isEdit && !!recordData?.checkinId"
              @keyup.enter="handleQueryVehicle"
            >
              <template #append>
                <el-button
                  :icon="Search"
                  :loading="queryLoading"
                  :disabled="isEdit && !!recordData?.checkinId"
                  @click="handleQueryVehicle"
                />
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" style="display: flex; align-items: center; padding-top: 32px;">
          <el-text type="info" size="small">{{ t('vehicleAutoFill') }}</el-text>
        </el-col>
      </el-row>

      <!-- 车辆信息区 -->
      <el-card :header="t('vehicleInfo')" class="mb-20">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('licensePlate')" prop="licensePlate">
              <el-input
                v-model="formData.licensePlate"
                :placeholder="t('licensePlatePlaceholder')"
                :readonly="vehicleInfoReadonly"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('vin')">
              <el-input
                v-model="formData.vin"
                :placeholder="t('vinPlaceholder')"
                :readonly="vehicleInfoReadonly"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('vehicleModel')">
              <el-input
                v-model="formData.vehicleModel"
                :placeholder="t('vehicleModel')"
                :readonly="vehicleInfoReadonly"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('vehicleConfiguration')">
              <el-input
                v-model="formData.vehicleConfiguration"
                :placeholder="t('vehicleConfiguration')"
                :readonly="vehicleInfoReadonly"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('color')">
              <el-input
                v-model="formData.color"
                :placeholder="t('color')"
                :readonly="vehicleInfoReadonly"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('mileage')">
              <el-input
                v-model.number="formData.mileage"
                type="number"
                :placeholder="t('mileagePlaceholder')"
                min="0"
              >
                <template #suffix>{{ t('mileageUnit') }}</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('vehicleAge')">
              <el-input
                v-model.number="formData.vehicleAge"
                type="number"
                placeholder="自动计算"
                readonly
              >
                <template #suffix>{{ t('vehicleAgeUnit') }}</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 客户信息区 -->
      <el-card :header="t('customerInfo')" class="mb-20">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('repairPersonName')" prop="repairPersonName">
              <el-input
                v-model="formData.repairPersonName"
                :placeholder="t('repairPersonNamePlaceholder')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('repairPersonPhone')" prop="repairPersonPhone">
              <el-input
                v-model="formData.repairPersonPhone"
                :placeholder="t('repairPersonPhonePlaceholder')"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('serviceAdvisor')" prop="serviceAdvisor">
              <el-input
                v-model="formData.serviceAdvisor"
                placeholder="当前用户"
                readonly
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 服务类型 -->
      <el-form-item :label="t('serviceType')" prop="serviceType">
        <el-input
          v-model="formData.serviceType"
          readonly
          disabled
        />
      </el-form-item>

      <!-- 备注信息 -->
      <el-form-item :label="t('notes')">
        <el-input
          v-model="formData.notes"
          type="textarea"
          :rows="3"
          :placeholder="t('notesPlaceholder')"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">{{ t('actions.cancel') }}</el-button>
      <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
        {{ t('actions.save') }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { getVehicleInfo } from '@/api/modules/afterSales/checkin';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { CheckinItem, CheckinFormData, VehicleSearchType } from '@/types/afterSales/checkin';
import type { FormInstance, FormRules } from 'element-plus';

// 国际化
const { t } = useModuleI18n('afterSales.checkin');

interface Props {
  visible: boolean;
  isEdit: boolean;
  recordData: CheckinItem | null;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'submit', data: CheckinFormData): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const formRef = ref<FormInstance>();
const queryLoading = ref(false);
const submitLoading = ref(false);
const queryLicensePlate = ref('');

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 车辆信息是否只读
const vehicleInfoReadonly = computed(() => {
  return props.isEdit && !!props.recordData?.checkinId;
});

// 表单数据
const formData = reactive<CheckinFormData>({
  licensePlate: '',
  vin: '',
  vehicleModel: '',
  vehicleConfiguration: '',
  color: '',
  mileage: undefined,
  vehicleAge: undefined,
  repairPersonName: '',
  repairPersonPhone: '',
  serviceAdvisor: '当前用户', // 默认当前用户
  serviceType: 'repair',
  notes: ''
});

// 表单验证规则
const rules: FormRules = {
  repairPersonName: [
    { required: true, message: t('validation.repairPersonNameRequired'), trigger: 'blur' }
  ],
  repairPersonPhone: [
    { required: true, message: t('validation.repairPersonPhoneRequired'), trigger: 'blur' },
    { pattern: /^01[3-9]\d{7,8}$/, message: t('validation.phoneFormatError'), trigger: 'blur' }
  ],
  serviceAdvisor: [
    { required: true, message: t('validation.serviceAdvisorRequired'), trigger: 'blur' }
  ],
  serviceType: [
    { required: true, message: t('validation.serviceTypeRequired'), trigger: 'blur' }
  ]
};

// 监听visible变化，初始化表单
watch(() => props.visible, (visible) => {
  if (visible) {
    initForm();
  }
});

// 初始化表单
const initForm = () => {
  if (props.isEdit && props.recordData) {
    // 编辑模式：填充现有数据
    Object.assign(formData, {
      licensePlate: props.recordData.licensePlate,
      vin: props.recordData.vin,
      vehicleModel: props.recordData.vehicleModel,
      vehicleConfiguration: props.recordData.vehicleConfiguration,
      color: props.recordData.color,
      mileage: props.recordData.mileage,
      vehicleAge: props.recordData.vehicleAge,
      repairPersonName: props.recordData.repairPersonName,
      repairPersonPhone: props.recordData.repairPersonPhone,
      serviceAdvisor: props.recordData.serviceAdvisor,
      serviceType: props.recordData.serviceType,
      notes: props.recordData.notes
    });
    queryLicensePlate.value = props.recordData.licensePlate;
  } else {
    // 新增模式：重置表单
    Object.assign(formData, {
      licensePlate: '',
      vin: '',
      vehicleModel: '',
      vehicleConfiguration: '',
      color: '',
      mileage: undefined,
      vehicleAge: undefined,
      repairPersonName: '',
      repairPersonPhone: '',
      serviceAdvisor: '当前用户',
      serviceType: 'repair',
      notes: ''
    });
    queryLicensePlate.value = '';
  }

  // 重置表单验证
  nextTick(() => {
    formRef.value?.clearValidate();
  });
};

// 查询车辆信息
const handleQueryVehicle = async () => {
  if (!queryLicensePlate.value.trim()) {
    ElMessage.warning(t('messages.pleaseEnterQuery'));
    return;
  }

  queryLoading.value = true;

  try {
    // 判断查询类型（简单判断：包含字母的是车牌号，纯数字的是车主IC）
    const searchType: VehicleSearchType = /[A-Za-z]/.test(queryLicensePlate.value)
      ? 'licensePlate'
      : 'ownerIC';

    let vehicleInfo = await getVehicleInfo(queryLicensePlate.value, searchType);
    vehicleInfo = vehicleInfo.result;
    // 自动填充车辆信息
    Object.assign(formData, {
      licensePlate: vehicleInfo.licensePlate,
      vin: vehicleInfo.vin,
      vehicleModel: vehicleInfo.vehicleModel,
      vehicleConfiguration: vehicleInfo.vehicleConfiguration,
      color: vehicleInfo.color,
      vehicleAge: vehicleInfo.vehicleAge
    });

    ElMessage.success(t('messages.queryVehicleSuccess'));
  } catch (error) {
    ElMessage.warning(t('messages.queryVehicleFailed'));
    // 查询失败时只填充车牌号
    if (/[A-Za-z]/.test(queryLicensePlate.value)) {
      formData.licensePlate = queryLicensePlate.value;
    }
  } finally {
    queryLoading.value = false;
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    submitLoading.value = true;

    // 移除客户端计算字段
    const submitData = { ...formData };
    delete submitData.vehicleAge;

    emit('submit', submitData);
  } catch (error) {
    console.error('表单验证失败:', error);
  } finally {
    submitLoading.value = false;
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
};
</script>

<style lang="scss" scoped>
.mb-20 {
  margin-bottom: 20px;
}

:deep(.el-card__header) {
  font-weight: 600;
}
</style>
