# 到店登记管理 API 接口文档

## 1. 分页查询到店登记列表

**请求信息**:
- **方法**: `GET`
- **路径**: `/api/v1/afterSales/checkins`

**请求参数**:
```java
public class CheckinPageQuery {
    @Parameter(description = "页码（从1开始）", example = "1")
    @Min(value = 1, message = "页码必须大于0")
    private Long pageNum = 1L;
    
    @Parameter(description = "每页条数", example = "10")
    @Min(value = 1, message = "每页条数必须大于0")
    @Max(value = 100, message = "每页条数不能超过100")
    private Long pageSize = 10L;
    
    @Parameter(description = "登记编号", example = "CK001")
    private String checkinId;
    
    @Parameter(description = "车牌号", example = "京A12345")
    private String licensePlate;
    
    @Parameter(description = "送修人姓名", example = "张三")
    private String repairPersonName;
    
    @Parameter(description = "送修人电话", example = "13812345678")
    private String repairPersonPhone;
    
    @Parameter(description = "登记开始时间", example = "2024-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createdAtStart;
    
    @Parameter(description = "登记结束时间", example = "2024-01-31")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createdAtEnd;
}
```

**响应数据** (符合MyBatis Plus IPage结构):
```java
public class CheckinPageVO {
    @Schema(description = "当前页数据列表")
    private List<CheckinVO> records;
    
    @Schema(description = "总记录数")
    private Long total;
    
    @Schema(description = "每页显示条数")
    private Long size;
    
    @Schema(description = "当前页码")
    private Long current;
    
    @Schema(description = "总页数")
    private Long pages;
}

public class CheckinVO {
    @Schema(description = "登记ID")
    private Long id;
    
    @Schema(description = "登记编号", example = "CK001")
    private String checkinId;
    
    @Schema(description = "车牌号", example = "京A12345")
    private String licensePlate;
    
    @Schema(description = "VIN码", example = "ABCD123456789")
    private String vin;
    
    @Schema(description = "车型", example = "Model1")
    private String vehicleModel;
    
    @Schema(description = "车辆配置", example = "高配版")
    private String vehicleConfiguration;
    
    @Schema(description = "颜色", example = "白色")
    private String color;
    
    @Schema(description = "里程数", example = "20000")
    private Integer mileage;
    
    @Schema(description = "车龄（月）", example = "24")
    private Integer vehicleAge;
    
    @Schema(description = "送修人姓名", example = "张三")
    private String repairPersonName;
    
    @Schema(description = "送修人电话", example = "13812345678")
    private String repairPersonPhone;
    
    @Schema(description = "服务顾问", example = "李顾问")
    private String serviceAdvisor;
    
    @Schema(description = "关联维修工单编号", example = "RO12345")
    private String relatedRepairOrderId;
    
    @Schema(description = "服务类型", example = "维修")
    private String serviceType;
    
    @Schema(description = "备注")
    private String notes;
    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;
    
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;
}
```

## 2. 根据车牌号查询车辆信息

**请求信息**:
- **方法**: `GET`  
- **路径**: `/api/v1/afterSales/checkins/vehicle-info/{licensePlate}`

**路径参数**:
- `licensePlate` (String): 车牌号

**响应数据**:
```java
public class VehicleInfoVO {
    @Schema(description = "车牌号", example = "京A12345")
    private String licensePlate;
    
    @Schema(description = "VIN码", example = "ABCD123456789")
    private String vin;
    
    @Schema(description = "车型", example = "Model1")
    private String vehicleModel;
    
    @Schema(description = "车辆配置", example = "高配版")
    private String vehicleConfiguration;
    
    @Schema(description = "颜色", example = "白色")
    private String color;
    
    @Schema(description = "车龄（月）", example = "24")
    private Integer vehicleAge;
}
```

## 3. 查询登记详情

**请求信息**:
- **方法**: `GET`
- **路径**: `/api/v1/afterSales/checkins/{id}`

**路径参数**:
- `id` (Long): 登记ID

**响应数据**: 
```java
// 使用 CheckinVO 对象（同分页查询中的CheckinVO）
```

## 4. 新增到店登记

**请求信息**:
- **方法**: `POST`
- **路径**: `/api/v1/afterSales/checkins`

**请求体**:
```java
public class CheckinCreateForm {
    @Schema(description = "车牌号", example = "京A12345", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "车牌号不能为空")
    private String licensePlate;
    
    @Schema(description = "VIN码", example = "ABCD123456789")
    private String vin;
    
    @Schema(description = "车型", example = "Model1")
    private String vehicleModel;
    
    @Schema(description = "车辆配置", example = "高配版")
    private String vehicleConfiguration;
    
    @Schema(description = "颜色", example = "白色")
    private String color;
    
    @Schema(description = "里程数", example = "20000")
    @Min(value = 0, message = "里程数不能为负数")
    private Integer mileage;
    
    @Schema(description = "送修人姓名", example = "张三", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "送修人姓名不能为空")
    private String repairPersonName;
    
    @Schema(description = "送修人电话", example = "13812345678", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "送修人电话不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String repairPersonPhone;
    
    @Schema(description = "服务顾问", example = "李顾问", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "服务顾问不能为空")
    private String serviceAdvisor;
    
    @Schema(description = "服务类型", example = "维修", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "服务类型不能为空")
    private String serviceType;
    
    @Schema(description = "备注")
    @Length(max = 500, message = "备注不能超过500个字符")
    private String notes;
}
```

**响应数据**:
```java
public class CheckinCreateVO {
    @Schema(description = "登记ID")
    private Long id;
    
    @Schema(description = "登记编号", example = "CK001")
    private String checkinId;
}
```

## 5. 编辑到店登记

**请求信息**:
- **方法**: `PUT`
- **路径**: `/api/v1/afterSales/checkins/{id}`

**路径参数**:
- `id` (Long): 登记ID

**请求体**:
```java
public class CheckinUpdateForm {
    @Schema(description = "里程数", example = "25000")
    @Min(value = 0, message = "里程数不能为负数")
    private Integer mileage;
    
    @Schema(description = "送修人姓名", example = "张三", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "送修人姓名不能为空")
    private String repairPersonName;
    
    @Schema(description = "送修人电话", example = "13812345678", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "送修人电话不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String repairPersonPhone;
    
    @Schema(description = "服务顾问", example = "李顾问", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "服务顾问不能为空")
    private String serviceAdvisor;
    
    @Schema(description = "备注")
    @Length(max = 500, message = "备注不能超过500个字符")
    private String notes;
}
```

**响应数据**: 
```java
// 无特定返回数据，使用空的 Result<Void>
```

## 6. 删除到店登记

**请求信息**:
- **方法**: `DELETE`
- **路径**: `/api/v1/afterSales/checkins/{id}`

**路径参数**:
- `id` (Long): 登记ID

**响应数据**: 
```java
// 无特定返回数据，使用空的 Result<Void>
```

## 7. 创建维修工单

**请求信息**:
- **方法**: `POST`
- **路径**: `/api/v1/afterSales/checkins/{id}/repair-order`

**路径参数**:
- `id` (Long): 登记ID

**响应数据**:
```java
public class RepairOrderCreateVO {
    @Schema(description = "维修工单ID")
    private Long repairOrderId;
    
    @Schema(description = "维修工单编号", example = "RO12345")
    private String repairOrderCode;
}
```

## 8. 导出登记列表

**请求信息**:
- **方法**: `GET`
- **路径**: `/api/v1/afterSales/checkins/export`

**请求参数**:
```java
public class CheckinExportQuery {
    @Parameter(description = "登记编号", example = "CK001")
    private String checkinId;
    
    @Parameter(description = "车牌号", example = "京A12345")
    private String licensePlate;
    
    @Parameter(description = "送修人姓名", example = "张三")
    private String repairPersonName;
    
    @Parameter(description = "送修人电话", example = "13812345678")
    private String repairPersonPhone;
    
    @Parameter(description = "登记开始时间", example = "2024-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createdAtStart;
    
    @Parameter(description = "登记结束时间", example = "2024-01-31")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createdAtEnd;
}
```

**响应数据**: 
```java
// 直接返回文件流
// Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
// Content-Disposition: attachment; filename="checkin-list.xlsx"
```