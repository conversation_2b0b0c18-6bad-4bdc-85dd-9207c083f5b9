/**
 * 数据字典类型常量
 * 用于统一管理所有字典类型编码
 * 基于 dictionary_category.sql 文件定义
 */

// 系统基础字典
export const DICTIONARY_TYPES = {
  // 语言类型 (保持静态配置)
  LANGUAGE_TYPE: '0001',
  // 状态
  COMMON_STATUS: '0002',

  // 业务状态字典
  PROJECT_TYPE: '0102',           // 项目类型
  PARTS_TYPE: '0103',             // 零件类型
  WORK_ORDER_PRIORITY: '0104',    // 工单优先级
  WORK_ORDER_STATUS: '0105',      // 工单状态
  CUSTOMER_SOURCE: '0106',        // 客户来源
  WORK_ORDER_TYPE: '0107',        // 工单类型
  APPOINTMENT_STATUS: '0108',     // 预约状态
  BUYER_TYPE: '0109',             // 买方类型
  ORDER_STATUS: '0111',           // 订单状态
  APPROVAL_STATUS: '0112',        // 审批状态
  TEST_DRIVE_CONFIG: '0113',      // 试驾配置
  ID_TYPE: '0114',                // 证件类别
  REQUISITION_STATUS: '0115',     // 申请状态
  INTENT_LEVEL: '0116',           // 意向级别
  ALLOCATION_STATUS: '0117',      // 配车状态
  SALES_TYPE: '0118',             // 销售类型
  PAYMENT_STATUS: '0119',         // 支付状态
  INVENTORY_STATUS: '0120',       // 库存状态
  INSURANCE_STATUS: '0121',       // 保险状态
  DELIVERY_STATUS: '0122',        // 交车状态
  CONFIRMATION_METHOD: '0123',    // 确认方式
  WORK_ASSIGNMENT_STATUS: '0124', // 工单分配状态
  APPROVAL_TYPE: '0125',          // 审批类型
  BOOLEAN_TYPE: '0126',           // 布尔值类型
  PARTS_NAME: '0127',             // 零件名称

  // 主数据字典
  VEHICLE_MODEL: '0110',          // 车型
  DEALER: '0128',                 // 经销商
  STORE: '0129',                  // 门店
  DEALER_STORE: '0135',           // 经销商门店
  SALES_CONSULTANT: '0130',       // 销售顾问
  SERVICE_ADVISOR: '0131',        // 服务顾问
  TECHNICIAN: '0132',             // 技师
  VEHICLE_COLOR: '0133',          // 车辆颜色
  WAREHOUSE: '0134',              // 仓库
  SUPPLIER: '0135',               // 供应商
  VEHICLE_VARIANT: '0136',        // 车型配置
  TIME_SLOT: '0137',              // 时间段
  PROSPECT_STATUS: '0138',        // 潜客状态
  FOLLOW_UP_METHOD: '0139',       // 跟进方式
  REGION: '0140',                 // 地区
  DEFEAT_REASON: '0141',          // 战败原因
  PAYMENT_METHOD: '0142',         // 付款方式
  JPJ_REGISTRATION_STATUS: '0143', // JPJ注册状态
  STORE_COUNT_TYPE: '0147',       // 关联门店数类型
  VEHICLE_LOCK_STATUS: '0144',    // 车辆锁定状态
  VEHICLE_INVOICE_STATUS: '0145', // 车辆开票状态
  LOAN_STATUS: '0146',            // 贷款状态
  VEHICLE_REGISTRATION_STATUS: '0148', // 车辆登记状态
  SALES_ADVISOR: '0149',           // 销售顾问

  // 基础数据
  STORE_PROPERTIES: '0200',       // 门店属性
  STORE_TYPE:'0201',             // 门店类型
  DEPARTMENT_TYPE: '0202',        // 部门类型
} as const;

// 字典类型值的类型定义
export type DictionaryType = typeof DICTIONARY_TYPES[keyof typeof DICTIONARY_TYPES];

// 字典项接口
export interface DictionaryOption {
  code: string;
  name: string;
}

// 字典分类映射 - 用于区分主数据和业务字典
export const DICTIONARY_CATEGORIES = {
  // 主数据类别 (Master Data)
  MASTER_DATA: [
    DICTIONARY_TYPES.VEHICLE_MODEL,
    DICTIONARY_TYPES.DEALER,
    DICTIONARY_TYPES.STORE,
    DICTIONARY_TYPES.DEALER_STORE,
    DICTIONARY_TYPES.SALES_CONSULTANT,
    DICTIONARY_TYPES.SERVICE_ADVISOR,
    DICTIONARY_TYPES.TECHNICIAN,
    DICTIONARY_TYPES.VEHICLE_COLOR,
    DICTIONARY_TYPES.WAREHOUSE,
    DICTIONARY_TYPES.SUPPLIER,
    DICTIONARY_TYPES.VEHICLE_VARIANT,
    DICTIONARY_TYPES.SALES_ADVISOR,
    DICTIONARY_TYPES.DEPARTMENT_TYPE,
  ],

  // 业务状态字典 (Business Status Dictionary)
  BUSINESS_STATUS: [
    DICTIONARY_TYPES.PROJECT_TYPE,
    DICTIONARY_TYPES.PARTS_TYPE,
    DICTIONARY_TYPES.WORK_ORDER_PRIORITY,
    DICTIONARY_TYPES.WORK_ORDER_STATUS,
    DICTIONARY_TYPES.CUSTOMER_SOURCE,
    DICTIONARY_TYPES.WORK_ORDER_TYPE,
    DICTIONARY_TYPES.APPOINTMENT_STATUS,
    DICTIONARY_TYPES.BUYER_TYPE,
    DICTIONARY_TYPES.ORDER_STATUS,
    DICTIONARY_TYPES.APPROVAL_STATUS,
    DICTIONARY_TYPES.TEST_DRIVE_CONFIG,
    DICTIONARY_TYPES.ID_TYPE,
    DICTIONARY_TYPES.REQUISITION_STATUS,
    DICTIONARY_TYPES.INTENT_LEVEL,
    DICTIONARY_TYPES.ALLOCATION_STATUS,
    DICTIONARY_TYPES.SALES_TYPE,
    DICTIONARY_TYPES.PAYMENT_STATUS,
    DICTIONARY_TYPES.INVENTORY_STATUS,
    DICTIONARY_TYPES.INSURANCE_STATUS,
    DICTIONARY_TYPES.DELIVERY_STATUS,
    DICTIONARY_TYPES.CONFIRMATION_METHOD,
    DICTIONARY_TYPES.WORK_ASSIGNMENT_STATUS,
    DICTIONARY_TYPES.APPROVAL_TYPE,
    DICTIONARY_TYPES.BOOLEAN_TYPE,
    DICTIONARY_TYPES.PARTS_NAME,
    DICTIONARY_TYPES.TIME_SLOT,
    DICTIONARY_TYPES.PROSPECT_STATUS,
    DICTIONARY_TYPES.FOLLOW_UP_METHOD,
    DICTIONARY_TYPES.REGION,
    DICTIONARY_TYPES.DEFEAT_REASON,
    DICTIONARY_TYPES.STORE_COUNT_TYPE,
    DICTIONARY_TYPES.VEHICLE_REGISTRATION_STATUS,
  ],
} as const;

// 检查是否为主数据字典
export const isMasterData = (dictionaryType: string): boolean => {
  return DICTIONARY_CATEGORIES.MASTER_DATA.includes(dictionaryType as DictionaryType);
};

// 检查是否为业务状态字典
export const isBusinessStatus = (dictionaryType: string): boolean => {
  return DICTIONARY_CATEGORIES.BUSINESS_STATUS.includes(dictionaryType as DictionaryType);
};
