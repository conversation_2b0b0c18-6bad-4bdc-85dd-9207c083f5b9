{"home": "Home", "title": "Base System", "loginTitle": "System Login", "loginButton": "Login Now", "login": "<PERSON><PERSON>", "logout": "Logout", "username": "Username", "password": "Password", "confirmPassword": "Confirm Password", "rememberMe": "Remember Me", "forgotPassword": "Forgot Password", "loginSuccess": "Login Successful", "loginFailed": "Login Failed", "usernameRequired": "Username is required", "user": {"title": "User Management", "addUser": "Add User", "editUser": "Edit User", "username": "Username", "fullName": "Full Name", "phone": "Phone", "email": "Email", "userType": "User Type", "userTypeFactory": "Factory", "userTypeStore": "Store", "entryDate": "Entry Date", "userStatus": "User Status", "userStatusNormal": "Normal", "userStatusDisabled": "Disabled", "primaryStore": "Primary Store", "enterUsername": "Enter username", "enterFullName": "Enter full name", "enterPhone": "Enter phone", "enterEmail": "Enter email", "selectUserType": "Select user type", "selectEntryDate": "Select entry date", "usernameRequired": "Username is required", "fullNameRequired": "Full name is required", "userTypeRequired": "User type is required", "entryDateRequired": "Entry date is required", "emailFormatError": "Invalid email format", "assignRoles": "Assign Roles", "currentUser": "Current User", "addAssignment": "Add Assignment", "primaryRole": "Primary Store", "belongStore": "Belong Store", "department": "Department", "roles": "Roles", "position": "Position", "enterPosition": "Enter position", "confirmDeleteUser": "Confirm to delete user {fullName}?", "editUserSuccess": "User status updated successfully", "factoryUserRoleLimit": "Factory user can only assign one store role", "roleAssignmentIncomplete": "Please complete store and role information", "selectBelongStore": "Select belong store"}, "role": {"title": "Role Management", "roleName": "Role Name", "roleCode": "Role Code", "roleStatus": "Role Status", "roleScope": "<PERSON>", "description": "Description", "menuPermissions": "Menu Permissions", "dataPermissions": "Data Permissions", "enterRoleName": "Enter role name", "enterRoleCode": "Enter role code", "selectRoleStatus": "Select role status", "selectRoleScope": "Select role scope", "enterDescription": "Enter description", "roleStatusNormal": "Normal", "roleStatusDisabled": "Disabled", "roleScopeAll": "All Data", "roleScopeCustom": "Custom Data", "roleScopeDepartment": "Department Data", "roleScopeDepartmentAndBelow": "Department and Below", "roleScopeOnlyPersonal": "Personal Data Only", "addRole": "Add Role", "editRole": "Edit Role", "deleteRole": "Delete Role", "configMenuPermission": "Config Menu Permission", "configDataPermission": "Config Data Permission", "exportRole": "Export Role", "roleNameRequired": "Role name is required", "roleCodeRequired": "Role code is required", "roleSourceRequired": "Role source is required", "roleStatusRequired": "Role status is required", "roleScopeRequired": "Role scope is required", "addRoleSuccess": "Role added successfully", "addRoleFailed": "Failed to add role", "editRoleSuccess": "Role updated successfully", "editRoleFailed": "Failed to update role", "deleteRoleSuccess": "Role deleted successfully", "deleteRoleFailed": "Failed to delete role", "configPermissionSuccess": "Permission configured successfully", "configPermissionFailed": "Failed to configure permission", "confirmDeleteRole": "Confirm to delete this role?", "cannotDeleteRoleInUse": "Role is in use and cannot be deleted", "expandCollapse": "Expand/Collapse", "selectAll": "Select All/None", "parentChildLinkage": "Parent-<PERSON>", "roleSource": "Role Source", "selectRoleSource": "Select role source", "roleSourceFactory": "Factory Role", "roleSourceStore": "Store Role", "configPermission": "Permission Config"}, "menu": {"title": "Menu Management", "menuName": "<PERSON>u Name", "menuCode": "Menu Code", "menuType": "Menu Type", "parentMenu": "<PERSON><PERSON>", "menuIcon": "Menu Icon", "menuPath": "<PERSON><PERSON>", "component": "Component Path", "permission": "Permission", "sortOrder": "Sort Order", "menuStatus": "Menu Status", "isVisible": "Is Visible", "isCache": "<PERSON>", "enterMenuName": "Enter menu name", "enterMenuCode": "Enter menu code", "selectMenuType": "Select menu type", "selectParentMenu": "Select parent menu", "selectMenuIcon": "Select menu icon", "enterMenuPath": "Enter menu path", "enterComponent": "Enter component path", "enterPermission": "Enter permission", "enterSortOrder": "Enter sort order", "selectMenuStatus": "Select menu status", "menuTypeDirectory": "Directory", "menuTypeMenu": "<PERSON><PERSON>", "menuTypeButton": "<PERSON><PERSON>", "menuStatusNormal": "Normal", "menuStatusDisabled": "Disabled", "addMenu": "<PERSON><PERSON>", "editMenu": "<PERSON>", "deleteMenu": "Delete Menu", "refreshMenuCache": "<PERSON><PERSON><PERSON>", "menuNameRequired": "<PERSON>u name is required", "menuCodeRequired": "Menu code is required", "menuTypeRequired": "Menu type is required", "menuPathRequired": "Menu path is required", "componentRequired": "Component is required", "permissionRequired": "Permission is required", "sortOrderRequired": "Sort order is required", "menuStatusRequired": "Menu status is required", "addMenuSuccess": "<PERSON><PERSON> added successfully", "addMenuFailed": "Failed to add menu", "editMenuSuccess": "Menu updated successfully", "editMenuFailed": "Failed to update menu", "deleteMenuSuccess": "<PERSON><PERSON> deleted successfully", "deleteMenuFailed": "Failed to delete menu", "refreshMenuCacheSuccess": "Menu cache refreshed successfully", "refreshMenuCacheFailed": "Failed to refresh menu cache", "confirmDeleteMenu": "Confirm to delete this menu?", "cannotDeleteMenuWithChildren": "Menu has sub-menus and cannot be deleted", "menuSide": "<PERSON><PERSON> Side", "selectMenuSide": "Select menu side", "menuSideDealer": "Dealer", "menuSideFactory": "Factory", "menuSideRequired": "Menu side is required", "yes": "Yes", "no": "No"}, "department": {"title": "Department Management", "departmentName": "Department Name", "departmentCode": "Department Code", "parentDepartment": "Parent Department", "departmentLevel": "Department Level", "departmentStatus": "Department Status", "departmentType": "Department Type", "departmentHead": "Department Head", "description": "Description", "enterDepartmentName": "Enter department name", "enterDepartmentCode": "Enter department code", "selectParentDepartment": "Select parent department", "selectDepartmentStatus": "Select department status", "selectDepartmentType": "Select department type", "selectDepartmentHead": "Select department head", "enterDescription": "Enter description", "departmentStatusNormal": "Normal", "departmentStatusDisabled": "Disabled", "departmentTypeBusiness": "Business", "departmentTypeSupport": "Support", "departmentTypeManagement": "Management", "addDepartment": "Add Department", "editDepartment": "Edit Department", "deleteDepartment": "Delete Department", "departmentNameRequired": "Department name is required", "departmentCodeRequired": "Department code is required", "departmentStatusRequired": "Department status is required", "departmentTypeRequired": "Department type is required", "belongStoreRequired": "Belong store is required", "addDepartmentSuccess": "Department added successfully", "addDepartmentFailed": "Failed to add department", "editDepartmentSuccess": "Department updated successfully", "editDepartmentFailed": "Failed to update department", "deleteDepartmentSuccess": "Department deleted successfully", "deleteDepartmentFailed": "Failed to delete department", "confirmDeleteDepartment": "Confirm to delete this department?", "cannotDeleteDepartmentWithChildren": "Department has sub-departments and cannot be deleted"}, "store": {"title": "Store Management", "storeName": "Store Name", "storeCode": "Store Code", "parentStore": "Parent Store", "storeType": "Store Type", "storeStatus": "Store Status", "storeAddress": "Store Address", "contactPerson": "Contact Person", "contactPhone": "Contact Phone", "manager": "Manager", "enterStoreName": "Enter store name", "enterStoreCode": "Enter store code", "selectParentStore": "Select parent store", "selectStoreType": "Select store type", "selectStoreStatus": "Select store status", "enterStoreAddress": "Enter store address", "enterContactPerson": "Enter contact person", "enterContactPhone": "Enter contact phone", "selectManager": "Select manager", "storeStatusNormal": "Normal", "storeStatusDisabled": "Disabled", "storeTypeMain": "Main Store", "storeTypeBranch": "Branch Store", "storeTypeWarehouse": "Warehouse", "storeTypeHeadquarter": "Headquarter", "addStore": "Add Store", "editStore": "Edit Store", "deleteStore": "Delete Store", "expandAll": "Expand All", "collapseAll": "Collapse All", "storeNameRequired": "Store name is required", "storeCodeRequired": "Store code is required", "storeTypeRequired": "Store type is required", "storeStatusRequired": "Store status is required", "contactPersonRequired": "Contact person is required", "contactPhoneRequired": "Contact phone is required", "addStoreSuccess": "Store added successfully", "addStoreFailed": "Failed to add store", "editStoreSuccess": "Store updated successfully", "editStoreFailed": "Failed to update store", "deleteStoreSuccess": "Store deleted successfully", "deleteStoreFailed": "Failed to delete store", "confirmDeleteStore": "Confirm to delete this store?", "cannotDeleteStoreWithChildren": "Store has sub-stores and cannot be deleted", "storeShortName": "Store Short Name", "continent": "Continent", "city": "City", "postalCode": "Postal Code", "detailAddress": "Detail Address", "storeProperties": "Store Properties", "propertySales": "Sales", "propertyAfterSales": "After-sales", "storeDetail": "Store Detail"}}