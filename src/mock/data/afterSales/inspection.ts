// src/mock/data/afterSales/inspection.ts

import type { 
  InspectionSearchParams, 
  InspectionPageResponse, 
  InspectionListItem,
  Technician,
  InspectionStatus,
  RegisterType,
  ServiceType
} from '@/types/afterSales/inspection.d.ts';

// 生成动态 Mock 数据
function generateMockInspectionData(): InspectionListItem[] {
  const data: InspectionListItem[] = [];
  const names = ['张三', '李四', '王五', '李华', '赵六', '王大锤', '钱七', '孙八'];
  const advisors = ['李四', '赵六', '钱七', '孙八'];
  const technicians = ['王五', '孙八', '李华'];
  const licensePlates = ['粤A12345', '京B67890', '沪C54321', '浙D98765', '苏E11111'];
  const vehicleModels = ['Model Y 2023', 'Model 3 2022', 'Model X 2024', 'Model S 2023'];
  const configs = ['长续航版', '标准续航版', '高性能版'];
  const colors = ['白色', '黑色', '蓝色', '红色', '银色'];
  const statuses: InspectionStatus[] = ['pending', 'in_progress', 'pending_confirm', 'confirmed'];
  const registerTypes: RegisterType[] = ['appointment', 'walk_in'];
  const serviceTypes: ServiceType[] = ['maintenance', 'repair'];

  for (let i = 1; i <= 28; i++) {
    const createDate = new Date();
    createDate.setDate(createDate.getDate() - Math.floor(Math.random() * 30));
    const updateDate = new Date(createDate.getTime() + Math.random() * 86400000);

    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const confirmTime = status === 'confirmed' ? 
      new Date(updateDate.getTime() + Math.random() * 86400000).toISOString().slice(0, 16).replace('T', ' ') : '';

    data.push({
      inspectionNo: `IF${String(i).padStart(8, '0')}`,
      inspectionStatus: status,
      repairmanName: names[Math.floor(Math.random() * names.length)],
      repairmanPhone: `138${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}`,
      licensePlateNo: licensePlates[Math.floor(Math.random() * licensePlates.length)],
      vehicleModel: vehicleModels[Math.floor(Math.random() * vehicleModels.length)],
      vehicleConfig: configs[Math.floor(Math.random() * configs.length)],
      color: colors[Math.floor(Math.random() * colors.length)],
      mileage: Math.floor(Math.random() * 100000) + 5000,
      vehicleAge: Math.floor(Math.random() * 60) + 6,
      serviceAdvisor: advisors[Math.floor(Math.random() * advisors.length)],
      technician: technicians[Math.floor(Math.random() * technicians.length)],
      registerType: registerTypes[Math.floor(Math.random() * registerTypes.length)],
      serviceType: serviceTypes[Math.floor(Math.random() * serviceTypes.length)],
      customerConfirmTime: confirmTime,
      createTime: createDate.toISOString().slice(0, 16).replace('T', ' '),
      updateTime: updateDate.toISOString().slice(0, 16).replace('T', ' '),
      inspectionContent: null
    });
  }
  
  return data.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime());
}

const mockInspectionData = generateMockInspectionData();

// 技师Mock数据
const mockTechnicians: Technician[] = [
  { id: '1', name: '王五', level: '高级技师', specialties: ['发动机', '变速箱'] },
  { id: '2', name: '孙八', level: '中级技师', specialties: ['电气系统', '空调'] },
  { id: '3', name: '李华', level: '初级技师', specialties: ['轮胎', '刹车'] }
];

export const getInspectionList = (params: InspectionSearchParams): Promise<InspectionPageResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockInspectionData];

      // 应用筛选条件
      if (params.inspectionNo) {
        filteredData = filteredData.filter(item => 
          item.inspectionNo.toLowerCase().includes(params.inspectionNo!.toLowerCase())
        );
      }

      if (params.inspectionStatus) {
        filteredData = filteredData.filter(item => 
          item.inspectionStatus === params.inspectionStatus
        );
      }

      if (params.licensePlateNo) {
        filteredData = filteredData.filter(item => 
          item.licensePlateNo.includes(params.licensePlateNo!)
        );
      }

      if (params.repairmanName) {
        filteredData = filteredData.filter(item => 
          item.repairmanName.includes(params.repairmanName!)
        );
      }

      if (params.technician) {
        filteredData = filteredData.filter(item => 
          item.technician.includes(params.technician!)
        );
      }

      if (params.repairmanPhone) {
        filteredData = filteredData.filter(item => 
          item.repairmanPhone.includes(params.repairmanPhone!)
        );
      }

      if (params.createTimeRange && params.createTimeRange.length === 2) {
        const [startDate, endDate] = params.createTimeRange;
        filteredData = filteredData.filter(item => {
          const itemDate = item.createTime.split(' ')[0];
          return itemDate >= startDate && itemDate <= endDate;
        });
      }

      // 分页处理
      const page = params.page || 1;
      const pageSize = params.pageSize || 10;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        list: filteredData.slice(start, end),
        total: filteredData.length,
      });
    }, 300);
  });
};

export const assignTechnician = (inspectionNo: string, technicianId: string): Promise<{ success: boolean }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockInspectionData.findIndex(item => item.inspectionNo === inspectionNo);
      if (index !== -1) {
        const technician = mockTechnicians.find(t => t.id === technicianId);
        if (technician) {
          mockInspectionData[index].technician = technician.name;
          mockInspectionData[index].inspectionStatus = 'in_progress';
          mockInspectionData[index].updateTime = new Date().toISOString().slice(0, 16).replace('T', ' ');
        }
      }
      resolve({ success: true });
    }, 500);
  });
};

export const submitForConfirm = (inspectionNo: string): Promise<{ success: boolean }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockInspectionData.findIndex(item => item.inspectionNo === inspectionNo);
      if (index !== -1) {
        mockInspectionData[index].inspectionStatus = 'pending_confirm';
        mockInspectionData[index].updateTime = new Date().toISOString().slice(0, 16).replace('T', ' ');
      }
      resolve({ success: true });
    }, 500);
  });
};

export const recallInspection = (inspectionNo: string): Promise<{ success: boolean }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockInspectionData.findIndex(item => item.inspectionNo === inspectionNo);
      if (index !== -1) {
        mockInspectionData[index].inspectionStatus = 'in_progress';
        mockInspectionData[index].updateTime = new Date().toISOString().slice(0, 16).replace('T', ' ');
      }
      resolve({ success: true });
    }, 500);
  });
};

export const customerConfirm = (inspectionNo: string, confirmTime: string): Promise<{ success: boolean }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockInspectionData.findIndex(item => item.inspectionNo === inspectionNo);
      if (index !== -1) {
        mockInspectionData[index].inspectionStatus = 'confirmed';
        mockInspectionData[index].customerConfirmTime = confirmTime;
        mockInspectionData[index].updateTime = new Date().toISOString().slice(0, 16).replace('T', ' ');
      }
      resolve({ success: true });
    }, 500);
  });
};
