# P02: 领域实体模型

**基于页面**: 到店登记管理  
**建模模式**: 创意模式 (架构师角色)  
**生成时间**: 2025-07-31

---

## A. 实体聚类与提炼分析

### 原始字段分析
基于P01中的原始字段，我进行如下分析和实体划分：

#### 实体划分逻辑分析：

**分析1**: 从P01中识别出的核心业务字段可以明确划分为三个主要实体集群：
- **到店登记实体集群**: `checkinId`, `createdAt`, `updatedAt`, `notes`, `serviceType`, `relatedRepairOrderId`
- **车辆信息实体集群**: `licensePlate`, `vin`, `vehicleModel`, `vehicleConfiguration`, `color`, `mileage`, `vehicleAge`  
- **客户服务实体集群**: `repairPersonName`, `repairPersonPhone`, `serviceAdvisor`

**分析2**: 字段重现模式分析，以下字段在多个UI位置重复出现，表明它们属于核心实体：
- 车辆相关字段(`licensePlate`, `vin`, `vehicleModel`等) 在搜索表单、数据表格、编辑弹窗、详情弹窗中都出现
- 客户相关字段(`repairPersonName`, `repairPersonPhone`) 同样高频出现
- 登记相关字段(`checkinId`, `serviceType`) 是业务主实体的核心标识

**合并决策**: 基于业务内聚性原则，我建议将实体合并优化为**单一主实体**模式：

### 实体设计决策

#### 决策1: 不拆分车辆实体
- **理由**: P01显示车辆信息总是与登记信息一起出现，无独立的车辆管理需求
- **影响**: 简化查询逻辑，提高性能，符合当前业务模式

#### 决策2: 保留外键关联设计
- **理由**: P01中`relatedRepairOrderId`字段表明存在与维修工单的松耦合关联
- **影响**: 支持"创建维修单"业务流程，维持系统可扩展性

#### 决策3: 字段名称标准化
- **理由**: 遵循数据库命名规范，将驼峰式转换为下划线式
- **影响**: 符合MySQL命名标准，便于后端开发

---

## B. 实体关系图 (E-R图)

```mermaid
erDiagram
    tt_after_sales_checkin {
        BIGINT id PK "主键ID"
        VARCHAR checkin_id UK "登记编号，业务主键"
        VARCHAR license_plate "车牌号"
        VARCHAR vin "VIN码"
        VARCHAR vehicle_model "车型"
        VARCHAR vehicle_configuration "车辆配置"
        VARCHAR color "颜色"
        INT mileage "里程数(公里)"
        INT vehicle_age "车龄(月)"
        VARCHAR repair_person_name "送修人姓名"
        VARCHAR repair_person_phone "送修人电话"
        VARCHAR service_advisor "服务顾问"
        VARCHAR related_repair_order_id "关联维修工单编号"
        VARCHAR service_type "服务类型"
        TEXT notes "备注信息"
        TINYINT is_deleted "是否删除"
        VARCHAR created_by "创建人ID"
        TIMESTAMP created_at "创建时间"
        VARCHAR updated_by "更新人ID"
        TIMESTAMP updated_at "更新时间"
    }
    
    tt_repair_order {
        BIGINT id PK "主键ID"
        VARCHAR repair_order_id UK "维修工单编号"
        VARCHAR checkin_id "关联登记编号"
        VARCHAR status "工单状态"
        TIMESTAMP created_at "创建时间"
    }
    
    tt_after_sales_checkin ||--o| tt_repair_order : "关联维修工单"
```

### 关系描述

1. **tt_after_sales_checkin ↔ tt_repair_order**: 一对一关系 (可选)
   - 一个到店登记记录可以关联零个或一个维修工单
   - 通过`related_repair_order_id`字段建立关联
   - 支持"创建维修单"业务流程

---

## C. 实体定义表

### 主实体: tt_after_sales_checkin (售后到店登记表)

| 实体名 (Entity) | 字段名 (Field) | 数据类型 | 中文语义 | 备注 |
|:---|:---|:---|:---|:---|
| `tt_after_sales_checkin` | `id` | `BIGINT` | 主键ID | 自增主键 |
| `tt_after_sales_checkin` | `checkin_id` | `VARCHAR(50)` | 登记编号 | 业务主键，唯一约束 |
| `tt_after_sales_checkin` | `license_plate` | `VARCHAR(20)` | 车牌号 | 支持查询索引 |
| `tt_after_sales_checkin` | `vin` | `VARCHAR(50)` | VIN码 | 车辆识别码 |
| `tt_after_sales_checkin` | `vehicle_model` | `VARCHAR(100)` | 车型 | 车辆型号 |
| `tt_after_sales_checkin` | `vehicle_configuration` | `VARCHAR(200)` | 车辆配置 | 配置信息 |
| `tt_after_sales_checkin` | `color` | `VARCHAR(50)` | 颜色 | 车辆颜色 |
| `tt_after_sales_checkin` | `mileage` | `INT` | 里程数 | 单位：公里 |
| `tt_after_sales_checkin` | `vehicle_age` | `INT` | 车龄 | 单位：月，可为空 |
| `tt_after_sales_checkin` | `repair_person_name` | `VARCHAR(50)` | 送修人姓名 | 必填字段 |
| `tt_after_sales_checkin` | `repair_person_phone` | `VARCHAR(20)` | 送修人电话 | 必填字段，支持查询 |
| `tt_after_sales_checkin` | `service_advisor` | `VARCHAR(50)` | 服务顾问 | 必填字段 |
| `tt_after_sales_checkin` | `related_repair_order_id` | `VARCHAR(50)` | 关联维修工单编号 | 外键关联，可为空 |
| `tt_after_sales_checkin` | `service_type` | `VARCHAR(20)` | 服务类型 | 默认"维修" |
| `tt_after_sales_checkin` | `notes` | `TEXT` | 备注信息 | 可为空 |
| `tt_after_sales_checkin` | `is_deleted` | `TINYINT(1)` | 是否删除 | 默认0，逻辑删除标记 |
| `tt_after_sales_checkin` | `created_by` | `VARCHAR(36)` | 创建人ID | 用户ID |
| `tt_after_sales_checkin` | `created_at` | `TIMESTAMP` | 创建时间 | 默认当前时间，支持查询 |
| `tt_after_sales_checkin` | `updated_by` | `VARCHAR(36)` | 更新人ID | 用户ID |
| `tt_after_sales_checkin` | `updated_at` | `TIMESTAMP` | 更新时间 | 自动更新 |

### 关联实体: tt_repair_order (维修工单表，外部引用)

| 实体名 (Entity) | 字段名 (Field) | 数据类型 | 中文语义 | 备注 |
|:---|:---|:---|:---|:---|
| `tt_repair_order` | `id` | `BIGINT` | 主键ID | 自增主键 |
| `tt_repair_order` | `repair_order_id` | `VARCHAR(50)` | 维修工单编号 | 业务主键，对应checkin表的related_repair_order_id |
| `tt_repair_order` | `checkin_id` | `VARCHAR(50)` | 关联登记编号 | 外键关联tt_after_sales_checkin.checkin_id |
| `tt_repair_order` | `status` | `VARCHAR(20)` | 工单状态 | 工单状态信息 |
| `tt_repair_order` | `created_at` | `TIMESTAMP` | 创建时间 | 工单创建时间 |

---

## D. 字段映射验证

### P01字段归属检查

基于P01中的原始字段，验证每个字段在P02中的归属情况：

| P01原始字段 | 归属实体 | P02标准化字段 | 验证状态 |
|:---|:---|:---|:---|
| `checkinId` | `tt_after_sales_checkin` | `checkin_id` | ✅ 已归入 |
| `licensePlate` | `tt_after_sales_checkin` | `license_plate` | ✅ 已归入 |
| `vin` | `tt_after_sales_checkin` | `vin` | ✅ 已归入 |
| `vehicleModel` | `tt_after_sales_checkin` | `vehicle_model` | ✅ 已归入 |
| `vehicleConfiguration` | `tt_after_sales_checkin` | `vehicle_configuration` | ✅ 已归入 |
| `color` | `tt_after_sales_checkin` | `color` | ✅ 已归入 |
| `mileage` | `tt_after_sales_checkin` | `mileage` | ✅ 已归入 |
| `vehicleAge` | `tt_after_sales_checkin` | `vehicle_age` | ✅ 已归入 |
| `repairPersonName` | `tt_after_sales_checkin` | `repair_person_name` | ✅ 已归入 |
| `repairPersonPhone` | `tt_after_sales_checkin` | `repair_person_phone` | ✅ 已归入 |
| `serviceAdvisor` | `tt_after_sales_checkin` | `service_advisor` | ✅ 已归入 |
| `relatedRepairOrderId` | `tt_after_sales_checkin` | `related_repair_order_id` | ✅ 已归入 |
| `serviceType` | `tt_after_sales_checkin` | `service_type` | ✅ 已归入 |
| `notes` | `tt_after_sales_checkin` | `notes` | ✅ 已归入 |
| `createdAt` | `tt_after_sales_checkin` | `created_at` | ✅ 已归入 |
| `updatedAt` | `tt_after_sales_checkin` | `updated_at` | ✅ 已归入 |
| `page` | 不适用 | 计算字段 | ✅ 前端分页参数 |
| `pageSize` | 不适用 | 计算字段 | ✅ 前端分页参数 |
| `total` | 不适用 | 计算字段 | ✅ 前端分页参数 |
| `licensePlateQuery` | 不适用 | 计算字段 | ✅ 前端查询参数 |
| `index` | 不适用 | 计算字段 | ✅ 前端显示序号 |

**验证结果**: P01中的所有核心业务字段均已在P02的实体结构中找到归属，无遗漏字段。

---

## E. 架构设计原则

### 实体划分原则

1. **单一职责原则**: 每个实体承担明确的业务职责
   - `tt_after_sales_checkin`: 负责到店登记的完整业务数据
   - `tt_repair_order`: 负责维修工单业务(外部引用)

2. **数据内聚性最大化**: 
   - 将车辆信息、客户信息、服务信息统一到`tt_after_sales_checkin`实体中
   - 避免过度拆分导致的复杂关联查询

3. **业务完整性**: 
   - 单个实体包含完整的到店登记业务信息
   - 支持P01中所有的CRUD操作需求

### 数据库设计规范遵循

1. **命名规范**: 
   - 表名使用`tt_`前缀 (业务表)
   - 字段名使用小写+下划线格式
   - 主键统一命名为`id`

2. **必需字段**: 
   - 包含完整的审计字段(`created_by`, `created_at`, `updated_by`, `updated_at`)
   - 支持逻辑删除(`is_deleted`)

3. **索引设计**: 
   - 业务主键(`checkin_id`)唯一索引
   - 查询字段(`license_plate`, `repair_person_phone`)普通索引
   - 时间字段(`created_at`)排序索引

---

## F. 建模总结

### 核心成果

✅ **实体数量**: 1个主实体 + 1个关联实体  
✅ **字段覆盖**: 19个业务字段 + 5个系统字段  
✅ **关系定义**: 1个一对一可选关联  
✅ **规范遵循**: 完全符合数据库设计规范  

### 设计优势

1. **简洁性**: 避免过度设计，单表解决核心业务需求
2. **完整性**: 覆盖P01中所有业务字段和交互需求  
3. **扩展性**: 保留与维修工单的关联接口
4. **标准性**: 严格遵循命名规范和表结构标准

### 下一步建议

1. 进入步骤3，基于P02实体模型生成API接口契约
2. 重点关注CRUD操作的完整性和查询性能优化
3. 确保API设计与前端交互需求完全对齐

---

**建模完成时间**: 2025-07-31  
**下一步**: 进入步骤3 - 接口契约生成与交叉验证