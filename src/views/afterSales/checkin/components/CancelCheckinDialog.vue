<template>
  <el-dialog
    v-model="dialogVisible"
:title="t('dialog.cancelTitle')"
    width="400px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-position="top"
    >
      <el-alert
:title="t('actions.cancel', '注意')"
        type="warning"
        show-icon
        :closable="false"
        class="mb-20"
      >
        {{ t('dialog.cancelWarning') }}
      </el-alert>
      
      <div class="record-info mb-20">
        <p><strong>{{ t('checkinId') }}：</strong>{{ recordData?.checkinId }}</p>
        <p><strong>{{ t('licensePlate') }}：</strong>{{ recordData?.licensePlate }}</p>
        <p><strong>{{ t('repairPersonName') }}：</strong>{{ recordData?.repairPersonName }}</p>
      </div>
      
      <el-form-item :label="t('dialog.cancelReason')" prop="reason">
        <el-input
          v-model="formData.reason"
          type="textarea"
          :rows="4"
          :placeholder="t('dialog.cancelReasonPlaceholder')"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">{{ t('actions.cancel') }}</el-button>
      <el-button 
        type="danger" 
        :loading="submitLoading" 
        @click="handleConfirm"
      >
        {{ t('actions.confirm') }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue';
import { ElMessageBox } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { CheckinItem } from '@/types/afterSales/checkin';
import type { FormInstance, FormRules } from 'element-plus';

// 国际化
const { t } = useModuleI18n('afterSales.checkin');

interface Props {
  visible: boolean;
  recordData: CheckinItem | null;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm', data: { id: number; reason: string }): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const formRef = ref<FormInstance>();
const submitLoading = ref(false);

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 表单数据
const formData = reactive({
  reason: ''
});

// 表单验证规则
const rules: FormRules = {
  reason: [
    { required: true, message: t('messages.pleaseEnterCancelReason'), trigger: 'blur' },
    { min: 5, message: t('messages.cancelReasonTooShort'), trigger: 'blur' }
  ]
};

// 监听visible变化，重置表单
watch(() => props.visible, (visible) => {
  if (visible) {
    formData.reason = '';
    nextTick(() => {
      formRef.value?.clearValidate();
    });
  }
});

// 确认取消
const handleConfirm = async () => {
  if (!formRef.value || !props.recordData) return;
  
  try {
    await formRef.value.validate();
    
    // 二次确认
    await ElMessageBox.confirm(
      t('dialog.cancelConfirm'),
      t('dialog.cancelTitle'),
      {
        confirmButtonText: t('actions.confirm'),
        cancelButtonText: t('actions.cancel'),
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    );
    
    submitLoading.value = true;
    
    emit('confirm', {
      id: props.recordData.id,
      reason: formData.reason
    });
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消操作失败:', error);
    }
  } finally {
    submitLoading.value = false;
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
};
</script>

<style lang="scss" scoped>
.mb-20 {
  margin-bottom: 20px;
}

.record-info {
  background-color: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
  border-left: 4px solid #409eff;
  
  p {
    margin: 0 0 8px 0;
    color: #606266;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    strong {
      color: #303133;
    }
  }
}
</style>