// src/api/modules/afterSales/inspection.ts

import request from '@/api';
import type {
  InspectionSearchParams,
  InspectionPageResponse
} from '@/types/afterSales/inspection.d.ts';
import {
  getInspectionList as getMockInspectionList,
  assign<PERSON><PERSON><PERSON><PERSON> as assignMockTechnician,
  submitForConfirm as submitMockForConfirm,
  recallInspection as recallMockInspection,
  customerConfirm as customerMockConfirm
} from '@/mock/data/afterSales/inspection';

const USE_MOCK_API_TEMP = true;

export const getInspectionList = (params: InspectionSearchParams): Promise<InspectionPageResponse> => {
  if (USE_MOCK_API_TEMP) {
    return getMockInspectionList(params);
  }
  return request.get<any, InspectionPageResponse>('/after-sales/inspection/list', { params });
};

export const assignTechnician = (inspectionNo: string, technicianId: string): Promise<{ success: boolean }> => {
  if (USE_MOCK_API_TEMP) {
    return assignMockTechnician(inspectionNo, technicianId);
  }
  return request.post<any, { success: boolean }>('/after-sales/inspection/assign', {
    inspectionNo,
    technicianId
  });
};

export const submitForConfirm = (inspectionNo: string): Promise<{ success: boolean }> => {
  if (USE_MOCK_API_TEMP) {
    return submitMockForConfirm(inspectionNo);
  }
  return request.post<any, { success: boolean }>(`/after-sales/inspection/${inspectionNo}/submit`);
};

export const recallInspection = (inspectionNo: string): Promise<{ success: boolean }> => {
  if (USE_MOCK_API_TEMP) {
    return recallMockInspection(inspectionNo);
  }
  return request.post<any, { success: boolean }>(`/after-sales/inspection/${inspectionNo}/recall`);
};

export const customerConfirm = (inspectionNo: string, confirmTime: string): Promise<{ success: boolean }> => {
  if (USE_MOCK_API_TEMP) {
    return customerMockConfirm(inspectionNo, confirmTime);
  }
  return request.post<any, { success: boolean }>(`/after-sales/inspection/${inspectionNo}/confirm`, {
    confirmTime
  });
};
