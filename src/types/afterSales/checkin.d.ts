// src/types/afterSales/checkin.d.ts

export interface CheckinListItem {
  checkinId: string;
  licensePlate: string;
  vin: string;
  vehicleModel: string;
  vehicleConfiguration: string;
  color: string;
  mileage?: number;
  vehicleAge?: number;
  repairPersonName: string;
  repairPersonPhone: string;
  serviceAdvisor: string;
  relatedRepairOrderId?: string | null;
  serviceType: string;
  createdAt: string;
  updatedAt: string;
  notes?: string;
  isDeleted: boolean;
}

export interface CheckinListParams {
  checkinId?: string;
  licensePlate?: string;
  repairPersonName?: string;
  repairPersonPhone?: string;
  createdAtStart?: string;
  createdAtEnd?: string;
  page?: number;
  pageSize?: number;
}

export interface CheckinPageResponse {
  list: CheckinListItem[];
  total: number;
}

export interface VehicleInfo {
  licensePlate: string;
  vin?: string;
  vehicleModel?: string;
  vehicleConfiguration?: string;
  color?: string;
  mileage?: number;
  vehicleAge?: number;
}

export interface CheckinFormData extends Omit<CheckinListItem, 'checkinId' | 'createdAt' | 'updatedAt'> {
  checkinId?: string;
}
