<template>
  <el-card class="search-card mb-20" shadow="never">
    <el-form class="search-form" label-position="top">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="t('department.departmentCode')">
            <el-input
              v-model="searchForm.departmentCode"
              :placeholder="tc('inputPlaceholder')"
              clearable
              @keyup.enter="handleSearch"
            />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item :label="t('department.departmentName')">
            <el-input
              v-model="searchForm.departmentName"
              :placeholder="tc('inputPlaceholder')"
              clearable
              @keyup.enter="handleSearch"
            />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item :label="t('department.departmentStatus')">
            <DictionarySelect
              v-model="searchForm.departmentStatus"
              :dictionary-type="DICTIONARY_TYPES.COMMON_STATUS"
              :placeholder="tc('pleaseSelect')"
              show-all
              :all-label="tc('all')"
              all-value=""
              clearable
            />
          </el-form-item>
        </el-col>

        <el-col :span="6" style="text-align: right;">
          <el-form-item label="&nbsp;">
            <el-button
              type="primary"
              :icon="Search"
              @click="handleSearch"
            >
              {{ tc('search') }}
            </el-button>
            <el-button
              :icon="RefreshLeft"
              @click="handleReset"
            >
              {{ tc('reset') }}
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
import { reactive } from 'vue';
import { Search, RefreshLeft } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import DictionarySelect from '@/components/common/DictionarySelect.vue';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import type { DepartmentSearchParams } from '@/types/base/department';

interface Emits {
  (e: 'search', params: DepartmentSearchParams): void;
  (e: 'reset'): void;
}

const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('base');

// 创建本地响应式表单数据
const searchForm = reactive<DepartmentSearchParams>({
  departmentName: '',
  departmentCode: '',
  departmentStatus: '',
  departmentType: ''
});

const handleSearch = () => {
  // 直接传递参数给父组件
  emit('search', { ...searchForm });
};

const handleReset = () => {
  console.log('🔄 子组件 handleReset 被调用');
  // 重置所有搜索条件
  searchForm.departmentName = '';
  searchForm.departmentCode = '';
  searchForm.departmentStatus = '';
  searchForm.departmentType = '';
  // 触发重置
  emit('reset');
};
</script>

<style lang="scss" scoped>
.search-card {
  margin-bottom: 20px;
}

.search-form {
  .el-form-item {
    margin-bottom: 0;
  }
  .el-form-item__label {
    margin-bottom: 8px;
  }
}

.mb-20 {
  margin-bottom: 20px;
}
</style>
