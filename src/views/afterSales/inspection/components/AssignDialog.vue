<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElDialog, ElForm, ElFormItem, ElSelect, ElOption, ElButton, ElDescriptions, ElDescriptionsItem } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { InspectionListItem } from '@/types/afterSales/inspection.d.ts';
import type { Technician } from '@/api/modules/masterData';

interface Props {
  visible: boolean;
  recordData: InspectionListItem | null;
  technicians: Technician[];
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm', technicianId: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.inspection');

const selectedTechnicianId = ref('');

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const currentRecord = computed(() => props.recordData);

// 重置表单
const resetForm = () => {
  selectedTechnicianId.value = '';
};

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm();
  }
});

// 确认分配
const handleConfirm = () => {
  if (!selectedTechnicianId.value) {
    return;
  }
  emit('confirm', selectedTechnicianId.value);
};

// 取消
const handleCancel = () => {
  dialogVisible.value = false;
};

const getRegisterTypeText = (type: string) => {
  return type === 'appointment' ? t('registerType.appointment') : t('registerType.walk_in');
};

const getServiceTypeText = (type: string) => {
  return type === 'maintenance' ? t('serviceType.maintenance') : t('serviceType.repair');
};
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="t('dialog.assignTitle')"
    width="600px"
    @close="resetForm"
  >
    <div v-if="currentRecord">
      <!-- 检查单信息 -->
      <el-descriptions :column="2" border class="mb-20">
        <el-descriptions-item :label="t('table.inspectionNo')">
          {{ currentRecord.inspectionNo }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.licensePlateNo')">
          {{ currentRecord.licensePlateNo }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.repairmanName')">
          {{ currentRecord.repairmanName }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.repairmanPhone')">
          {{ currentRecord.repairmanPhone }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.vehicleModel')">
          {{ currentRecord.vehicleModel }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.color')">
          {{ currentRecord.color }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.registerType')">
          {{ getRegisterTypeText(currentRecord.registerType) }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.serviceType')">
          {{ getServiceTypeText(currentRecord.serviceType) }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 技师选择 -->
      <el-form label-position="top">
        <el-form-item :label="t('dialog.technicianLabel')" required>
          <el-select
            v-model="selectedTechnicianId"
            :placeholder="t('messages.selectTechnician')"
            style="width: 100%"
          >
            <el-option
              v-for="technician in technicians"
              :key="technician.id"
              :label="`${technician.name} (${technician.level})`"
              :value="technician.id"
            >
              <div>
                <span>{{ technician.name }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ technician.level }}</span>
              </div>
              <div style="font-size: 12px; color: #999">
                专长：{{ technician.specialties.join('、') }}
              </div>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">{{ tc('cancel') }}</el-button>
        <el-button 
          type="primary" 
          @click="handleConfirm"
          :disabled="!selectedTechnicianId"
        >
          {{ tc('confirm') }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.mb-20 {
  margin-bottom: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  .el-button {
    margin-left: 10px;
  }
}
</style>
