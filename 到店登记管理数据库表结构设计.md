# 到店登记管理数据库表结构设计

## 1. 设计概述

### 1.1 设计目标
基于到店登记管理API接口文档和数据库规范，设计到店登记业务的核心数据表结构，支持到店登记业务的完整生命周期管理。

### 1.2 设计原则
- **命名规范**: 遵循 `tt_` 业务表前缀规范
- **字段完整**: 包含所有必需的审计字段
- **索引优化**: 基于业务查询需求设计合理索引
- **数据完整性**: 通过合理约束保证数据质量
- **扩展性**: 考虑未来业务发展需求

### 1.3 涉及模块
- **售后服务**: 到店登记管理核心业务

## 2. 核心业务表设计

### 2.1 到店登记表 (tt_afterSales_checkin)

```sql
CREATE TABLE `tt_afterSales_checkin` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
  
  -- 登记基础信息
  `checkin_code` VARCHAR(50) NOT NULL COMMENT '登记编号：格式为门店简称+YYYYMMDD+三位流水号',
  `dealer_code` VARCHAR(20) NOT NULL COMMENT '经销商代码',
  `checkin_status` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '登记状态：1-正常，2-已取消',
  `cancel_reason` TEXT DEFAULT NULL COMMENT '取消原因',
  `service_type` VARCHAR(20) NOT NULL DEFAULT 'REPAIR' COMMENT '服务类型：REPAIR-维修，MAINTENANCE-保养',
  
  -- 车辆信息
  `license_plate` VARCHAR(20) NOT NULL COMMENT '车牌号',
  `vin_code` VARCHAR(30) DEFAULT NULL COMMENT 'VIN码',
  `vehicle_model` VARCHAR(100) DEFAULT NULL COMMENT '车型',
  `vehicle_configuration` VARCHAR(100) DEFAULT NULL COMMENT '车辆配置',
  `vehicle_color` VARCHAR(50) DEFAULT NULL COMMENT '车辆颜色',
  `vehicle_mileage` INT DEFAULT NULL COMMENT '里程数（公里）',
  `vehicle_age_months` INT DEFAULT NULL COMMENT '车龄（月）',
  
  -- 客户信息
  `repair_person_name` VARCHAR(100) NOT NULL COMMENT '送修人姓名',
  `repair_person_phone` VARCHAR(20) NOT NULL COMMENT '送修人电话',
  `repair_person_ic` VARBINARY(255) DEFAULT NULL COMMENT '送修人身份证号/IC号（加密存储）',
  `is_owner` TINYINT(1) DEFAULT '1' COMMENT '是否车主本人：1-是，0-否',
  
  -- 服务信息
  `service_advisor_code` VARCHAR(50) NOT NULL COMMENT '服务顾问编码',
  `service_advisor_name` VARCHAR(100) NOT NULL COMMENT '服务顾问姓名',
  `appointment_time` TIMESTAMP NULL DEFAULT NULL COMMENT '预约时间',
  `checkin_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '登记时间',
  
  -- 关联信息
  `related_repair_order_code` VARCHAR(50) DEFAULT NULL COMMENT '关联环检单编号',
  `related_repair_order_id` BIGINT DEFAULT NULL COMMENT '关联环检单ID',
  
  -- 业务信息
  `customer_complaint` TEXT DEFAULT NULL COMMENT '客户反映问题',
  `initial_diagnosis` TEXT DEFAULT NULL COMMENT '初步诊断',
  `notes` TEXT DEFAULT NULL COMMENT '备注信息',
  
  -- 必需字段（审计字段）
  `is_deleted` TINYINT(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `created_by` VARCHAR(36) DEFAULT NULL COMMENT '创建人ID',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` VARCHAR(36) DEFAULT NULL COMMENT '更新人ID',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  -- 索引设计
  UNIQUE KEY `uk_checkin_code` (`checkin_code`),
  KEY `idx_dealer_code` (`dealer_code`),
  KEY `idx_license_plate` (`license_plate`),
  KEY `idx_vin_code` (`vin_code`),
  KEY `idx_repair_person_phone` (`repair_person_phone`),
  KEY `idx_service_advisor_code` (`service_advisor_code`),
  KEY `idx_checkin_status` (`checkin_status`),
  KEY `idx_checkin_time` (`checkin_time`),
  KEY `idx_related_repair_order_code` (`related_repair_order_code`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_dealer_code_checkin_time` (`dealer_code`, `checkin_time`),
  KEY `idx_status_time` (`checkin_status`, `checkin_time`),
  KEY `idx_advisor_time` (`service_advisor_code`, `checkin_time`)
  
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='到店登记表';
```

## 3. 字段设计说明

### 3.1 核心业务字段

#### 登记标识字段
- **`checkin_code`**: 登记编号，格式为`门店简称+YYYYMMDD+三位流水号`，如：`KL20240731001`
- **`dealer_code`**: 经销商代码，关联基础数据中的经销商信息
- **`checkin_status`**: 登记状态，支持正常/已取消两种状态
- **`service_type`**: 服务类型，当前固定为维修服务

#### 车辆信息字段
- **`license_plate`**: 车牌号，必填字段，支持马来西亚车牌格式
- **`vin_code`**: VIN码，通过车辆信息查询自动填充
- **`vehicle_model`**: 车型名称
- **`vehicle_configuration`**: 车辆配置
- **`vehicle_color`**: 车辆颜色
- **`vehicle_mileage`**: 当前里程数
- **`vehicle_age_months`**: 车龄（以月为单位）

#### 客户信息字段
- **`repair_person_name`**: 送修人姓名，必填
- **`repair_person_phone`**: 送修人电话，必填，支持马来西亚手机号格式
- **`repair_person_ic`**: 身份证号/IC号，加密存储保护隐私
- **`is_owner`**: 标识送修人是否为车主本人

#### 服务信息字段
- **`service_advisor_code`**: 服务顾问编码
- **`service_advisor_name`**: 服务顾问姓名
- **`checkin_time`**: 登记时间，默认为当前时间

#### 关联信息字段
- **`related_repair_order_code`**: 关联的环检单编号
- **`related_repair_order_id`**: 关联的环检单ID

### 3.2 数据类型选择说明

- **字符串类型**: 使用`VARCHAR`，长度根据业务需求设定
- **整数类型**: ID使用`BIGINT`，状态使用`TINYINT`，里程数使用`INT`
- **时间类型**: 使用`TIMESTAMP`，支持自动时间戳更新
- **文本类型**: 备注等长文本使用`TEXT`
- **加密类型**: 敏感信息使用`VARBINARY`存储加密数据

## 4. 索引设计策略

### 4.1 主键索引
- **`PRIMARY KEY (id)`**: 自增主键，保证记录唯一性

### 4.2 唯一索引
- **`uk_checkin_code`**: 登记编号唯一索引，保证编号不重复

### 4.3 单字段索引
- **`idx_dealer_code`**: 经销商代码索引，支持按经销商查询
- **`idx_license_plate`**: 车牌号索引，支持车牌号模糊搜索
- **`idx_vin_code`**: VIN码索引，支持VIN码精确查询 
- **`idx_repair_person_phone`**: 送修人电话索引，支持电话号码查询
- **`idx_service_advisor_code`**: 服务顾问索引，支持顾问工作量查询
- **`idx_checkin_status`**: 登记状态索引，支持状态筛选
- **`idx_checkin_time`**: 登记时间索引，支持时间范围查询
- **`idx_related_repair_order_code`**: 环检单编号索引，支持关联查询
- **`idx_created_at`**: 创建时间索引，支持数据统计

### 4.4 复合索引
- **`idx_dealer_code_checkin_time`**: 经销商+时间复合索引，支持门店时间范围查询
- **`idx_status_time`**: 状态+时间复合索引，支持状态时间统计
- **`idx_advisor_time`**: 顾问+时间复合索引，支持顾问工作量统计

## 5. 序列号生成规则

### 5.1 登记编号生成函数

```sql
DELIMITER $$

CREATE FUNCTION `generate_checkin_code`(dealer_code VARCHAR(20)) 
RETURNS VARCHAR(50)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE today_date VARCHAR(8);
    DECLARE sequence_num INT DEFAULT 1;
    DECLARE checkin_code VARCHAR(50);
    
    -- 获取当前日期（YYYYMMDD格式）
    SET today_date = DATE_FORMAT(NOW(), '%Y%m%d');
    
    -- 查询当天该经销商的最大序列号
    SELECT COALESCE(MAX(CAST(RIGHT(checkin_code, 3) AS UNSIGNED)), 0) + 1
    INTO sequence_num
    FROM tt_afterSales_checkin 
    WHERE checkin_code LIKE CONCAT(dealer_code, today_date, '%')
    AND is_deleted = 0;
    
    -- 生成登记编号：经销商代码 + 日期 + 三位序列号
    SET checkin_code = CONCAT(dealer_code, today_date, LPAD(sequence_num, 3, '0'));
    
    RETURN checkin_code;
END$$

DELIMITER ;
```

### 5.2 使用示例

```sql
-- 生成登记编号示例
SELECT generate_checkin_code('KL') AS new_checkin_code;
-- 结果示例: KL20240731001

-- 在插入数据时使用
INSERT INTO tt_afterSales_checkin (
    checkin_code, dealer_code, license_plate, 
    repair_person_name, repair_person_phone, 
    service_advisor_code, service_advisor_name
) VALUES (
    generate_checkin_code('KL'), 'KL', 'WKL1234A',
    'Ahmad Ali', '**********',
    'SA001', 'Sarah Lim'
);
```

## 6. 数据安全设计

### 6.1 敏感信息加密

```sql
-- 身份证号/IC号加密存储函数
DELIMITER $$

CREATE FUNCTION `encrypt_ic`(ic_number VARCHAR(30)) 
RETURNS VARBINARY(255)
READS SQL DATA
DETERMINISTIC
BEGIN
    -- 使用AES加密算法加密身份证号
    RETURN CASE 
        WHEN ic_number IS NULL OR ic_number = '' THEN NULL
        ELSE AES_ENCRYPT(ic_number, 'DMS-CHECKIN-SECRET-KEY-2024')
    END;
END$$

CREATE FUNCTION `decrypt_ic`(encrypted_ic VARBINARY(255)) 
RETURNS VARCHAR(30)
READS SQL DATA
DETERMINISTIC
BEGIN
    -- 解密身份证号
    RETURN CASE 
        WHEN encrypted_ic IS NULL THEN NULL
        ELSE AES_DECRYPT(encrypted_ic, 'DMS-CHECKIN-SECRET-KEY-2024')
    END;
END$$

DELIMITER ;
```

### 6.2 数据访问审计

```sql
-- 创建数据访问审计触发器
DELIMITER $$

CREATE TRIGGER `tr_checkin_audit_insert` 
AFTER INSERT ON `tt_afterSales_checkin`
FOR EACH ROW
BEGIN
    INSERT INTO tt_data_access_audit (
        table_name, record_id, operation_type, 
        user_id, access_time
    ) VALUES (
        'tt_afterSales_checkin', NEW.id, 'INSERT',
        NEW.created_by, NOW()
    );
END$$

CREATE TRIGGER `tr_checkin_audit_update` 
AFTER UPDATE ON `tt_afterSales_checkin`
FOR EACH ROW
BEGIN
    INSERT INTO tt_data_access_audit (
        table_name, record_id, operation_type, 
        user_id, access_time
    ) VALUES (
        'tt_afterSales_checkin', NEW.id, 'UPDATE',
        NEW.updated_by, NOW()
    );
END$$

DELIMITER ;
```

## 7. 查询优化建议

### 7.1 常用查询模式

#### 分页查询优化
```sql
-- 推荐的分页查询方式
SELECT * FROM tt_afterSales_checkin 
WHERE is_deleted = 0
  AND dealer_code = 'KL'
  AND checkin_status = 1
  AND checkin_time >= '2024-07-01'
  AND checkin_time <= '2024-07-31'
ORDER BY checkin_time DESC
LIMIT 10 OFFSET 0;

-- 使用复合索引 idx_dealer_code_checkin_time 优化查询
```

#### 条件筛选查询
```sql
-- 利用多个单字段索引的查询
SELECT * FROM tt_afterSales_checkin 
WHERE is_deleted = 0
  AND (license_plate LIKE '%WKL%' 
       OR repair_person_phone LIKE '%0123%'
       OR repair_person_name LIKE '%Ahmad%')
ORDER BY created_at DESC;
```

### 7.2 统计查询优化

```sql
-- 经销商登记量统计
SELECT 
    dealer_code,
    checkin_status,
    DATE(checkin_time) as checkin_date,
    COUNT(*) as checkin_count
FROM tt_afterSales_checkin 
WHERE is_deleted = 0
  AND checkin_time >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY dealer_code, checkin_status, DATE(checkin_time)
ORDER BY checkin_date DESC, dealer_code;

-- 使用 idx_dealer_code_checkin_time 和 idx_status_time 索引
```

## 8. 数据维护策略

### 8.1 数据归档

```sql
-- 数据归档存储过程（归档1年前的数据）
DELIMITER $$

CREATE PROCEDURE `archive_checkin_data`()
BEGIN
    DECLARE archive_date DATE DEFAULT DATE_SUB(CURDATE(), INTERVAL 1 YEAR);
    DECLARE archived_count INT DEFAULT 0;
    
    START TRANSACTION;
    
    -- 统计需要归档的数据量
    SELECT COUNT(*) INTO archived_count
    FROM tt_afterSales_checkin 
    WHERE created_at < archive_date AND is_deleted = 0;
    
    -- 标记为已删除（软删除）
    UPDATE tt_afterSales_checkin 
    SET is_deleted = 1, updated_at = NOW()
    WHERE created_at < archive_date AND is_deleted = 0;
    
    -- 记录归档日志
    INSERT INTO tt_system_log (
        log_type, log_content, created_at
    ) VALUES (
        'DATA_ARCHIVE', 
        CONCAT('归档到店登记数据 ', archived_count, ' 条，归档日期: ', archive_date),
        NOW()
    );
    
    COMMIT;
    
    SELECT CONCAT('成功归档 ', archived_count, ' 条登记数据') as result;
END$$

DELIMITER ;
```

### 8.2 数据清理

```sql
-- 物理删除已软删除超过30天的数据
DELIMITER $$

CREATE PROCEDURE `cleanup_deleted_checkin_data`()
BEGIN
    DECLARE cleanup_date DATE DEFAULT DATE_SUB(CURDATE(), INTERVAL 30 DAY);
    DECLARE deleted_count INT DEFAULT 0;
    
    -- 统计要清理的数据
    SELECT COUNT(*) INTO deleted_count
    FROM tt_afterSales_checkin 
    WHERE is_deleted = 1 AND updated_at < cleanup_date;
    
    -- 物理删除数据
    DELETE FROM tt_afterSales_checkin 
    WHERE is_deleted = 1 AND updated_at < cleanup_date;
    
    -- 优化表
    OPTIMIZE TABLE tt_afterSales_checkin;
    
    SELECT CONCAT('清理了 ', deleted_count, ' 条已删除数据') as result;
END$$

DELIMITER ;
```

## 9. 性能监控

### 9.1 表统计信息

```sql
-- 查看表的统计信息
SELECT 
    TABLE_NAME,
    TABLE_ROWS,
    DATA_LENGTH,
    INDEX_LENGTH,
    (DATA_LENGTH + INDEX_LENGTH) as TOTAL_SIZE
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'dms_database' 
  AND TABLE_NAME = 'tt_afterSales_checkin';
```

### 9.2 索引使用分析

```sql
-- 分析索引使用情况
SELECT 
    INDEX_NAME,
    COLUMN_NAME,
    CARDINALITY,
    SUB_PART,
    NULLABLE
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = 'dms_database' 
  AND TABLE_NAME = 'tt_afterSales_checkin'
ORDER BY INDEX_NAME, SEQ_IN_INDEX;
```

## 10. 部署检查清单

### 10.1 表结构验证
- [ ] 表名符合命名规范（tt_前缀）
- [ ] 包含所有必需的审计字段
- [ ] 字段类型和长度合理
- [ ] 主键和唯一索引正确
- [ ] 普通索引覆盖查询需求
- [ ] 字符集设置为utf8mb4
- [ ] 存储引擎设置为InnoDB

### 10.2 安全性验证
- [ ] 敏感字段加密存储
- [ ] 审计触发器正常工作
- [ ] 访问权限设置合理
- [ ] 备份策略已配置

### 10.3 性能验证
- [ ] 索引创建成功
- [ ] 查询执行计划合理
- [ ] 插入/更新性能满足要求
- [ ] 存储过程功能正常

---

## 附录

### A. 预估数据量和性能指标

| 指标 | 预估值 | 说明 |
|------|--------|------|
| 年数据量 | 50万条 | 基于业务规模预估 |
| 日均新增 | 1,400条 | 平均每天新增登记数 |
| 峰值并发 | 100 QPS | 高峰期查询并发数 |
| 单表大小 | 2GB | 包含索引的表大小 |
| 查询响应时间 | <100ms | 95%的查询在100ms内完成 |
| 插入响应时间 | <50ms | 单条插入操作响应时间 |

### B. 常见业务场景SQL示例

```sql
-- 1. 按条件分页查询登记列表
SELECT * FROM tt_afterSales_checkin 
WHERE is_deleted = 0
  AND dealer_code = ?
  AND checkin_status = ?
  AND DATE(checkin_time) BETWEEN ? AND ?
ORDER BY checkin_time DESC
LIMIT ? OFFSET ?;

-- 2. 根据车牌号查询登记记录
SELECT * FROM tt_afterSales_checkin 
WHERE is_deleted = 0
  AND license_plate = ?
ORDER BY checkin_time DESC;

-- 3. 查询服务顾问的工作量
SELECT 
    service_advisor_code,
    service_advisor_name,
    COUNT(*) as checkin_count,
    COUNT(CASE WHEN related_repair_order_code IS NOT NULL THEN 1 END) as completed_count
FROM tt_afterSales_checkin 
WHERE is_deleted = 0
  AND DATE(checkin_time) = CURDATE()
GROUP BY service_advisor_code, service_advisor_name;

-- 4. 更新登记状态为已取消
UPDATE tt_afterSales_checkin 
SET checkin_status = 2,
    cancel_reason = ?,
    updated_by = ?,
    updated_at = NOW()
WHERE id = ? AND checkin_status = 1 AND related_repair_order_code IS NULL;
```

---

**文档版本**: v1.0  
**创建日期**: 2025-07-31  
**维护团队**: 数据库设计团队  
**审核状态**: 待审核