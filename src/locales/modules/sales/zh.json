{"salesProspectManagement": "销售潜客管理", "prospectId": "潜客ID", "inputProspectId": "请输入潜客ID", "prospectName": "潜客名称", "inputProspectName": "请输入潜客名称", "prospectPhone": "潜客手机号", "inputPhoneNumber": "请输入手机号", "sourceChannel": "来源渠道", "prospectLevel": "潜客级别", "prospectStatus": "潜客状态", "intentModel": "意向车型", "intentVariant": "意向配置", "intentColor": "意向颜色", "salesAdvisorId": "销售顾问工号", "salesAdvisorName": "销售顾问", "prospectCreationTime": "潜客创建时间", "lastFollowUpTime": "上次跟进时间", "nextFollowUpTime": "下次跟进时间", "markNoIntention": "标记无意向", "changeAdvisor": "变更顾问", "addProspectSuccess": "新增潜客成功", "followUpRecordAddSuccess": "跟进记录添加成功", "markNoIntentionApplySuccess": "标记无意向申请提交成功", "changeAdvisorSuccess": "变更顾问成功", "levelH": "H级", "levelA": "A级", "levelB": "B级", "levelC": "C级", "statusNew": "新建", "statusFollowing": "跟进中", "statusClosed": "已成交", "statusNoIntention": "无意向", "addProspect": "新增潜客", "foundMatchingLead": "找到匹配线索", "noMatchingLead": "未找到匹配线索", "customerName": "客户姓名", "phoneNumber": "手机号", "email": "邮箱", "idType": "身份证件类别", "idNumber": "身份证件号", "inputNameOrPhoneRequired": "请至少输入客户姓名或手机号", "noMatchingLeadMessage": "系统中没有找到匹配的线索信息，无法新增潜客。请确认输入的客户信息是否正确，或联系管理员先创建线索。", "inputIdNumber": "请输入证件号码", "inputEmail": "请输入邮箱地址", "intentionLevel": "意向级别", "idCard": "身份证", "passport": "护照", "residencePermit": "居住证", "inputProspectNameRequired": "请输入潜客名称", "inputPhoneNumberRequired": "请输入手机号", "invalidPhoneNumber": "请输入正确的手机号码", "selectIdTypeRequired": "请选择证件类别", "inputIdNumberRequired": "请输入证件号码", "inputEmailRequired": "请输入邮箱", "invalidEmail": "请输入正确的邮箱地址", "selectIntentionLevelRequired": "请选择意向级别", "prospectFollowUp": "潜客跟进", "prospectInfo": "潜客信息", "idTypePlaceholder": "请选择", "region": "地区", "selectRegion": "请选择地区", "prospectIntention": "潜客意向", "selectIntentModel": "请选择意向Model", "selectIntentVariant": "请选择意向配置", "selectIntentColor": "请选择意向颜色", "followUpRecord": "跟进记录", "currentAdvisor": "当前顾问", "followUpMethod": "跟进方式", "selectFollowUpMethod": "请选择跟进方式", "followUpTime": "跟进时间", "selectFollowUpTime": "请选择跟进时间", "intentionLevelPlaceholder": "请选择", "nextFollowUpTimePlaceholder": "系统会根据意向级别自动计算", "followUpDetails": "跟进情况", "followUpDetailsPlaceholder": "请描述跟进情况...", "inputFollowUpDetailsRequired": "请填写跟进情况", "selectFollowUpMethodRequired": "请选择跟进方式", "selectFollowUpTimeRequired": "请选择跟进时间", "selectIntentionLevelRequiredFollowUp": "请选择意向级别", "selectNextFollowUpTimeRequired": "请选择下次跟进时间", "selectIntentionModelRequired": "请选择意向Model", "selectIntentionVariantRequired": "请选择意向配置", "selectIntentionColorRequired": "请选择意向颜色", "changeAdvisorModalTitle": "变更顾问", "currentAdvisorId": "当前顾问工号", "currentAdvisorName": "当前顾问名字", "changeAdvisorId": "变更顾问工号", "changeAdvisorName": "变更顾问名字", "selectNewAdvisor": "请选择新的销售顾问", "changeReason": "重新分配顾问原因", "inputChangeReason": "请输入重新分配顾问原因", "newAdvisorCannotBeSame": "新顾问不能与当前顾问相同", "changeFailed": "变更失败", "selectNewAdvisorRequired": "请选择新的销售顾问", "inputChangeReasonRequired": "请输入重新分配顾问原因", "markNoIntentionConfirm": "标记无意向确认", "markTime": "标记时间", "noIntentionReason": "无意向原因", "selectNoIntentionReason": "请选择无意向原因", "detailedDescription": "详细说明", "detailedDescriptionPlaceholder": "请详细描述无意向原因...", "inputDetailedDescriptionRequired": "请输入详细说明", "markNoIntentionSubmitSuccess": "标记无意向申请提交成功，等待审核", "formValidationFailed": "表单验证失败或提交失败", "prospectDetails": "潜客详情", "prospectSource": "潜客来源", "idDocumentType": "身份证件类别", "idDocumentNumber": "身份证件号", "address": "地址", "testDriveRecord": "试驾记录", "driver": "试驾人", "model": "车型", "time": "时间", "feedback": "反馈", "noTestDriveRecord": "暂无试驾记录", "changeLog": "变更日志", "changeContent": "变更内容", "originalInfo": "原始信息", "changedInfo": "变更后信息", "operator": "操作人", "operationTime": "操作时间", "noFollowUpRecord": "暂无跟进记录", "noChangeLog": "暂无变更日志", "testDriveManagement": "试驾管理", "testDriveList": "试驾登记列表", "testDriveRegistration": "试驾登记", "testDriveDetail": "试驾详情", "testDriveEdit": "编辑试驾", "testDriveCreate": "登记试驾单", "testDriveNo": "试驾单号", "testDriveTime": "试驾时间", "testDriveModel": "试驾车型", "testDriveVariant": "试驾配置", "testDrivePerson": "试驾人", "testDrivePersonPhone": "试驾人手机号", "testDrivePersonIdCard": "试驾人证件号", "testDrivePersonLicense": "试驾人驾照号", "testDriveStartMileage": "试驾开始里程数", "testDriveEndMileage": "试驾结束里程数", "testDriveStartTime": "试驾开始时间", "testDriveEndTime": "试驾结束时间", "testDriveFeedback": "试驾反馈", "testDriveEntryTime": "试驾单录入时间", "searchProspect": "搜索潜客", "prospectSearch": "快捷搜索潜客", "testDriveInfo": "试驾信息", "salesConsultant": "销售顾问", "pleaseSelectModel": "请选择车型", "pleaseSelectVariant": "请选择配置", "pleaseEnterDriverName": "请输入试驾人姓名", "pleaseEnterDriverPhone": "请输入试驾人手机号", "pleaseSelectIdType": "请选择证件类别", "pleaseEnterIdNumber": "请输入试驾人证件号", "pleaseEnterLicenseNumber": "请输入试驾人驾照号", "pleaseEnterStartMileage": "请输入开始里程数", "pleaseEnterEndMileage": "请输入结束里程数", "pleaseSelectStartTime": "请选择试驾开始时间", "pleaseSelectEndTime": "请选择试驾结束时间", "pleaseEnterFeedback": "请输入试驾反馈", "autoFillAfterSearch": "搜索后自动填入", "autoGenerateOnSave": "保存时自动生成", "belongingConsultant": "潜客所属顾问", "defaultFromProspect": "默认带入潜客信息，可更改", "registrationSuccess": "登记成功", "updateSuccess": "更新试驾单成功", "searchProspectFirst": "请搜索并选择潜客", "needSalesConsultant": "潜客需要有所属销售顾问", "endMileageError": "结束里程数不能小于开始里程数", "endTimeError": "结束时间必须晚于开始时间", "pleaseEnterProspectName": "请输入潜客名称搜索", "pleaseEnterProspectPhone": "输入手机号搜索", "searchSuccess": "潜客信息加载成功", "noMatchingProspect": "未找到匹配的潜客信息", "pleaseEnterNameOrPhone": "请输入潜客名称或手机号进行搜索", "syncSuccess": "同步成功", "errorMessage": "错误消息", "recordCount": "记录数量", "syncTime": "同步时间", "No": "编号", "prospects": {"title": "潜客管理", "salesProspectManagement": "销售潜客管理", "prospectId": "潜客ID", "prospectName": "潜客名称", "prospectPhone": "潜客手机号", "sourceChannel": "来源渠道", "prospectLevel": "意向等级", "prospectStatus": "潜客状态", "intentModel": "意向车型", "intentVariant": "意向车款", "intentColor": "意向颜色", "salesAdvisorId": "销售顾问编号", "salesAdvisorName": "销售顾问", "prospectAssociatedTime": "关联潜客时间", "prospectCreationTime": "潜客创建时间", "lastFollowUpTime": "上次跟进时间", "nextFollowUpTime": "下次跟进时间", "markNoIntention": "标记无意向", "changeAdvisor": "变更顾问", "addProspect": "新增潜客", "prospectFollowUp": "潜客跟进", "prospectDetails": "潜客详情", "addProspectSuccess": "新增潜客成功", "followUpRecordAddSuccess": "跟进记录添加成功", "markNoIntentionApplySuccess": "无意向标记申请成功", "changeAdvisorSuccess": "顾问变更成功", "inputProspectId": "请输入潜客编号", "inputProspectName": "请输入潜客姓名", "inputPhoneNumber": "请输入联系电话", "prospectInfo": "潜客信息", "searchExistingLead": "搜索现有线索", "existingLeadFound": "找到现有线索", "existingLeadInfo": "系统检测到该客户已有线索记录，将基于现有线索创建潜客。", "foundMatchingLead": "找到匹配线索", "noMatchingLead": "未找到匹配线索", "noMatchingLeadMessage": "系统中没有找到匹配的线索信息，将创建为全新潜客。", "inputNameOrPhoneRequired": "请至少输入客户姓名或手机号", "nameRequired": "请输入潜客名称", "nameLength": "名称长度应在 2 到 50 个字符之间", "phoneRequired": "请输入手机号", "phoneFormat": "请输入有效的手机号码", "sourceRequired": "请选择来源渠道", "levelRequired": "请选择意向级别", "emailFormat": "请输入有效的邮箱地址", "searchCriteriaRequired": "请输入姓名或电话进行搜索", "noExistingLeadFound": "未找到现有线索，请填写以下表单创建新潜客。", "selectSourceChannel": "请选择来源渠道", "selectProspectLevel": "请选择意向级别", "selectIdType": "请选择证件类型", "inputIdNumber": "请输入证件号码", "inputEmail": "请输入邮箱地址", "inputRegion": "请输入地区", "idType": "证件类型", "idNumber": "证件号码", "email": "电子邮箱", "region": "所在地区", "changeAdvisorModalTitle": "变更销售顾问", "currentAdvisorInfo": "当前顾问信息", "currentAdvisor": "当前顾问", "noAdvisorAssigned": "未分配顾问", "changeAdvisorInfo": "变更信息", "newAdvisor": "新销售顾问", "selectNewAdvisor": "请选择新的销售顾问", "changeReason": "变更原因", "inputChangeReason": "请输入变更原因", "newAdvisorRequired": "必须选择一个新的销售顾问", "changeReasonRequired": "必须填写变更原因", "changeReasonLength": "原因说明应在 5 到 200 个字符之间", "newAdvisorCannotBeSame": "新顾问不能与当前顾问相同", "changeFailed": "变更操作失败", "markNoIntentionConfirm": "确认标记为无意向", "applicationInfo": "申请信息", "defeatReason": "标记无意向原因", "selectDefeatReason": "请选择标记无意向原因", "defeatDetails": "详细说明", "inputDefeatDetails": "请输入详细说明", "applicationTime": "申请时间", "defeatReasonRequired": "必须选择标记无意向原因", "defeatDetailsRequired": "必须填写详细说明", "defeatDetailsLength": "详细说明应在 10 到 500 个字符之间", "confirmMarkNoIntention": "确认标记", "markNoIntentionSubmitSuccess": "标记无意向申请已提交", "formValidationFailed": "表单校验失败", "followUpInfo": "跟进信息", "followUpType": "跟进方式", "selectFollowUpType": "请选择跟进方式", "followUpTime": "跟进时间", "selectFollowUpTime": "请选择跟进时间", "followUpContent": "跟进内容", "inputFollowUpContent": "请输入跟进内容", "followUpResult": "跟进结果", "inputFollowUpResult": "请输入跟进结果", "intentLevelAfterFollowUp": "跟进后意向", "selectIntentLevel": "请选择意向级别", "selectNextFollowUpTime": "请选择下次跟进时间", "followUpTypeRequired": "必须选择跟进方式", "followUpTimeRequired": "必须选择跟进时间", "followUpContentRequired": "必须填写跟进内容", "followUpContentLength": "跟进内容应在 10 到 500 个字符之间", "followUpResultRequired": "必须填写跟进结果", "followUpResultLength": "跟进结果应在 5 到 300 个字符之间", "intentLevelRequired": "必须选择跟进后的意向级别", "idTypePlaceholder": "请选择证件类型", "idCard": "身份证", "passport": "护照", "residencePermit": "居住证", "prospectIntention": "潜客意向", "selectIntentVariant": "请选择意向配置", "selectIntentColor": "请选择意向颜色", "followUpRecord": "跟进记录", "followUpMethod": "跟进方式", "selectFollowUpMethod": "请选择跟进方式", "intentionLevel": "意向级别", "intentionLevelPlaceholder": "请选择意向级别", "nextFollowUpTimePlaceholder": "系统将根据意向级别自动计算", "followUpDetails": "跟进详情", "followUpDetailsPlaceholder": "请描述跟进详情...", "inputFollowUpDetailsRequired": "请填写跟进详情", "selectIntentionLevelRequiredFollowUp": "请选择跟进后的意向级别", "selectNextFollowUpTimeRequired": "请选择下次跟进时间", "selectIntentionModelRequired": "请选择意向车型", "selectIntentionVariantRequired": "请选择意向配置", "selectIntentionColorRequired": "请选择意向颜色"}, "orders": {"title": "销售订单", "list": {"title": "销售订单列表", "search": {"title": "搜索条件", "orderNumber": "订单编号", "buyerName": "购车人姓名", "buyerPhone": "购车人电话", "buyerType": "购车人类型", "model": "车型", "orderStatus": "订单状态", "approvalStatus": "审批状态", "paymentStatus": "支付状态", "insuranceStatus": "保险状态", "loanApprovalStatus": "贷款审批状态", "jpjRegistrationStatus": "JPJ注册状态", "createTime": "创建时间", "ordererName": "下单人姓名", "ordererPhone": "下单人电话", "search": "搜索", "reset": "重置"}, "columns": {"orderNumber": "订单编号", "createTime": "创建时间", "customerName": "客户姓名", "customerPhone": "客户电话", "buyerName": "购车人姓名", "buyerPhone": "购车人电话", "buyerType": "购车人类型", "model": "车型", "variant": "配置", "color": "颜色", "vin": "VIN码", "paymentMethod": "付款方式", "orderStatus": "订单状态", "approvalStatus": "审批状态", "paymentStatus": "支付状态", "insuranceStatus": "保险状态", "jpjRegistrationStatus": "JPJ注册状态", "totalAmount": "总金额"}, "actions": {"view": "查看", "edit": "编辑", "delete": "删除", "submitApproval": "提交审核", "cancelOrder": "取消订单"}}, "detail": {"title": "订单详情", "orderNumber": "订单编号", "createTime": "创建时间", "orderStatus": "订单状态", "approvalStatus": "审批状态", "paymentStatus": "支付状态", "insuranceStatus": "保险状态", "jpjRegistrationStatus": "JPJ注册状态", "customerInfo": "客户信息", "ordererName": "下单人姓名", "ordererPhone": "下单人电话", "buyerName": "购车人姓名", "buyerPhone": "购车人电话", "buyerIdType": "证件类型", "buyerIdNumber": "证件号码", "buyerEmail": "邮箱", "buyerAddress": "地址", "buyerState": "州", "buyerCity": "城市", "buyerPostcode": "邮编", "buyerType": "购车人类型", "storeInfo": "门店信息", "storeRegion": "门店区域", "storeCity": "门店城市", "storeName": "门店名称", "salesConsultantName": "销售顾问", "otrFeesTab": "OTR费用", "model": "车型", "variant": "配置", "color": "颜色", "salesSubtotal": "车辆价格", "numberPlatesFee": "车牌费", "vin": "VIN码", "accessoriesInformation": "配件信息", "accessoryCategory": "配件类别", "accessoryName": "配件名称", "unitPrice": "单价", "quantity": "数量", "totalPrice": "总价", "accessoriesTotalAmount": "配件总金额", "invoiceTypeLabel": "开票类型", "invoiceNameLabel": "开票名称", "invoicePhoneLabel": "开票电话", "invoiceAddressLabel": "开票地址", "rightsInfo": "权益信息", "rightCode": "权益编码", "rightName": "权益名称", "rightMode": "权益类型", "discountAmount": "优惠金额", "effectiveDate": "生效日期", "expiryDate": "失效日期", "rightsDiscountTotalAmount": "权益优惠总金额", "paymentMethod": "付款方式", "loanStatusLabel": "贷款状态", "depositAmountLabel": "定金金额", "loanAmountLabel": "贷款金额", "finalPaymentLabel": "尾款金额", "insuranceInfo": "保险信息", "policyNumber": "保单号", "insuranceType": "保险类型", "insuranceCompany": "保险公司", "insurancePrice": "保险费用", "remarksLabel": "备注", "insuranceTotalAmount": "保险总金额", "otrFeesInfo": "OTR费用信息", "ticketNumber": "票据号", "feeItem": "费用项目", "feePrice": "费用金额", "otrFeesTotalAmount": "OTR费用总金额", "changeRecords": "变更记录", "operationTime": "操作时间", "operator": "操作人", "originalContent": "原内容", "changedContent": "变更内容", "content": "内容", "totalAmount": "总金额", "remainingReceivable": "剩余应收", "missingOrderNumber": "缺少订单编号", "fetchDetailError": "获取订单详情失败", "returnToList": "返回列表", "vehicleInfoTab": "车辆信息", "invoicingInfoTab": "开票信息", "rightsInfoTab": "服务&权益信息", "paymentInfoTab": "付款信息", "insuranceInfoTab": "保险信息", "purchaseInfo": "购车信息"}, "edit": {"title": "编辑订单", "orderNumber": "订单编号", "createTime": "创建时间", "customerInformation": "客户信息", "ordererNameLabel": "下单人姓名", "ordererPhoneLabel": "下单人电话", "buyerNameLabel": "购车人姓名", "buyerPhoneLabel": "购车人电话", "buyerIdTypeLabel": "证件类型", "buyerIdNumberLabel": "证件号码", "buyerEmailLabel": "邮箱", "buyerAddressLabel": "地址", "buyerStateLabel": "州", "buyerCityLabel": "城市", "buyerPostcodeLabel": "邮编", "buyerTypeLabel": "购车人类型", "dealershipInformation": "经销商信息", "regionLabel": "区域", "cityLabel": "城市", "dealershipLabel": "经销商", "salesAdvisorLabel": "销售顾问", "purchaseInformation": "购车信息", "vehicleInformation": "车辆信息", "modelLabel": "车型", "variantLabel": "配置", "colorLabel": "颜色", "salesSubtotalLabel": "车辆价格", "numberPlatesFeeLabel": "车牌费", "vinLabel": "VIN码", "invoiceInfoTab": "开票信息", "invoicingType": "开票类型", "invoicingName": "开票名称", "invoicingPhone": "开票电话", "invoicingAddress": "开票地址", "addRights": "添加权益", "rightsDiscountAmount": "权益优惠金额", "loanApprovalStatusField": "贷款审批状态", "depositAmount": "定金金额", "loanAmount": "贷款金额", "balanceAmount": "尾款金额", "insuranceNotes": "保险备注", "otrInfoTab": "OTR费用", "totalInvoiceAmount": "总开票金额", "remainingAmount": "剩余金额", "save": "保存", "saveSuccess": "保存成功", "saveFailed": "保存失败", "confirmCancel": "确认取消", "cancelSuccess": "取消成功", "validation": {"selectColor": "请选择颜色", "selectPaymentMethod": "请选择付款方式", "enterLoanAmount": "请输入贷款金额"}, "messages": {"saveSuccess": "保存成功", "saveFailed": "保存失败", "colorChangeAlert": "颜色变更需要重新审核"}}, "customerInformation": "客户信息", "ordererNameLabel": "下单人姓名", "ordererPhoneLabel": "下单人电话", "buyerNameLabel": "购车人姓名", "buyerPhoneLabel": "购车人电话", "buyerIdTypeLabel": "证件类型", "buyerIdNumberLabel": "证件号码", "buyerEmailLabel": "邮箱", "buyerAddressLabel": "地址", "buyerStateLabel": "州", "buyerCityLabel": "城市", "buyerPostcodeLabel": "邮编", "buyerTypeLabel": "购车人类型", "dealershipInformation": "经销商信息", "regionLabel": "区域", "cityLabel": "城市", "dealershipLabel": "经销商", "salesAdvisorLabel": "销售顾问", "purchaseInformation": "购车信息", "vehicleInformation": "车辆信息", "modelLabel": "车型", "variantLabel": "配置", "colorLabel": "颜色", "salesSubtotalLabel": "车辆价格", "numberPlatesFeeLabel": "车牌费", "vinLabel": "VIN码", "invoiceInfoTab": "开票信息", "invoicingType": "开票类型", "invoicingName": "开票名称", "invoicingPhone": "开票电话", "invoicingAddress": "开票地址", "addRights": "添加权益", "rightsDiscountAmount": "权益优惠金额", "loanApprovalStatusField": "贷款审批状态", "depositAmount": "定金金额", "loanAmount": "贷款金额", "balanceAmount": "尾款金额", "insuranceNotes": "保险备注", "otrInfoTab": "OTR费用", "totalInvoiceAmount": "总开票金额", "remainingAmount": "剩余金额", "rightCode": "权益编码", "rightName": "权益名称", "rightMode": "权益类型", "discountAmount": "优惠金额", "effectiveDate": "生效日期", "expiryDate": "失效日期", "model": "车型", "variant": "配置", "color": "颜色", "vin": "VIN码", "salesSubtotal": "车辆价格", "numberPlatesFee": "车牌费", "accessoriesInformation": "配件信息", "accessoryCategory": "配件类别", "accessoryName": "配件名称", "unitPrice": "单价", "quantity": "数量", "totalPrice": "总价", "accessoriesTotalAmount": "配件总金额", "invoiceTypeLabel": "开票类型", "invoiceNameLabel": "开票名称", "invoicePhoneLabel": "开票电话", "invoiceAddressLabel": "开票地址", "rightsInfo": "权益信息", "rightsDiscountTotalAmount": "权益优惠总金额", "paymentMethod": "付款方式", "loanStatusLabel": "贷款状态", "depositAmountLabel": "定金金额", "loanAmountLabel": "贷款金额", "finalPaymentLabel": "尾款金额", "insuranceInfo": "保险信息", "policyNumber": "保单号", "insuranceType": "保险类型", "insuranceCompany": "保险公司", "insurancePrice": "保险费用", "remarksLabel": "备注", "insuranceTotalAmount": "保险总金额", "otrFeesInfo": "OTR费用信息", "ticketNumber": "票据号", "feeItem": "费用项目", "feePrice": "费用金额", "otrFeesTotalAmount": "OTR费用总金额", "changeRecords": "变更记录", "operationTime": "操作时间", "operator": "操作人", "originalContent": "原内容", "changedContent": "变更内容", "totalAmount": "总金额", "remainingReceivable": "剩余应收", "salesOrderEdit": {"colorChangeNotice": "颜色变更将需要重新审核", "loanTermLabel": "贷款期限", "fullPaymentAmount": "全款金额", "placeholders": {"selectColor": "请选择颜色", "selectPaymentMethod": "请选择付款方式", "selectApprovalStatus": "请选择审批状态", "selectLoanTerm": "请选择贷款期限", "insuranceNotes": "请输入保险备注"}, "loanTerms": {"12": "12个月", "24": "24个月", "36": "36个月", "48": "48个月", "60": "60个月", "72": "72个月"}, "messages": {"fetchDetailFailed": "获取订单详情失败"}}, "rightsSelectionDialog": {"title": "选择权益", "rightCodePlaceholder": "请输入权益编码", "rightNamePlaceholder": "请输入权益名称", "availableRights": "可选权益", "selectedRights": "已选权益", "totalCount": "共 {count} 项", "selectedCount": "已选 {count} 项", "noRightsSelected": "暂无选择权益", "totalDiscountAmount": "总优惠金额", "confirmButtonText": "确认选择({count}项)", "fetchRightsError": "获取权益列表失败"}, "status": {"orderStatus": {"submitted": "已提交", "confirmed": "已确认", "pending_delivery": "待交付", "completed": "已完成", "canceled": "已取消"}, "buyerType": {"individual": "个人", "company": "公司"}, "paymentMethod": {"full_payment": "全款", "installment": "分期"}, "approvalStatus": {"pending_approval": "待审核", "approved": "已通过", "rejected": "已拒绝"}, "paymentStatus": {"pending_deposit": "待付定金", "fully_paid": "已付清", "refund_completed": "退款完成"}, "insuranceStatus": {"not_insured": "未投保", "pending": "处理中", "insured": "已投保"}, "jpjRegistrationStatus": {"pending_registration": "待注册", "registering": "注册中", "registered": "已注册", "registration_failed": "注册失败"}}}, "orderApproval": {"pageTitle": "订单审批管理", "pendingTab": "待审批", "approvedTab": "已审批", "searchTitle": "筛选条件", "approvalType": "审批类型", "approvalTypePlaceholder": "请选择审批类型", "orderNumber": "订单编号", "orderNumberPlaceholder": "请输入订单编号", "submittedBy": "提交人", "submittedByPlaceholder": "请输入提交人", "submissionTime": "提交时间", "startDate": "开始日期", "endDate": "结束日期", "approvalResult": "审批结果", "approvalResultPlaceholder": "请选择审批结果", "store": "门店", "storePlaceholder": "请选择门店", "serialNumber": "序号", "approvalNumber": "审批单号", "applicationReason": "申请原因", "approvalTime": "审批时间", "approvedBy": "审批人", "review": "审核", "pendingApprovalList": "待审批列表", "approvedList": "已审批列表", "totalCount": "共 {count} 条记录", "fetchDataFailed": "获取数据失败", "orderDetailNotImplemented": "订单详情功能待实现", "operationSuccess": "操作成功", "exportSuccess": "导出成功", "approvalTypeOptions": {"cancel_order": "取消订单", "modify_info": "修改信息", "price_adjustment": "价格调整"}, "approvalResultOptions": {"approved": "已通过", "rejected": "已拒绝", "pending": "待审批"}, "approvalDetail": "审批详情", "approvalInfo": "审批信息", "approvalComment": "审批意见", "cancelOrderApproval": "取消订单", "modifyOrderApproval": "修改信息", "approved": "已通过", "rejected": "已拒绝", "cancelReason": "取消原因", "changeDetails": "变更详情", "changedField": "变更字段", "originalValue": "原始值", "newValue": "新值", "comments": "审批意见", "dialogs": {"approveTitle": "审批操作", "resultRequired": "请选择审批结果", "reasonRequired": "请输入拒绝原因", "approveSuccess": "审批通过", "rejectSuccess": "审批拒绝", "exportTitle": "导出审批数据", "historyTitle": "审批历史"}, "result": {"approved": "通过", "rejected": "拒绝", "timeout": "超时"}, "placeholders": {"enterComments": "请输入审批意见", "enterReason": "请输入拒绝原因"}, "export": {"format": "导出格式", "range": "导出范围", "currentPage": "当前页", "filteredResult": "筛选结果", "allData": "全部数据"}, "timeRange": "时间范围", "approver": "审批人", "emptyHistory": "暂无审批历史"}, "delivery": {"pageTitle": "交付管理", "searchTitle": "筛选条件", "listTitle": "交付列表", "totalCount": "共 {count} 条记录", "deliveryNumber": "交车单号", "deliveryNumberPlaceholder": "请输入交车单号", "orderNumber": "订单编号", "orderNoPlaceholder": "请输入订单编号", "customerName": "客户姓名", "customerNamePlaceholder": "请输入客户姓名", "customerPhone": "客户电话", "customerMobilePlaceholder": "请输入客户电话", "vin": "车架号", "vinPlaceholder": "请输入车架号", "model": "车型", "variant": "配置", "color": "颜色", "orderStatus": "订单状态", "orderStatusPlaceholder": "请选择订单状态", "dealerStore": "经销商门店", "dealerStorePlaceholder": "请选择门店", "salesConsultant": "销售顾问", "salesmanPlaceholder": "请输入销售顾问", "deliveryStatus": "交付状态", "deliveryStatusPlaceholder": "请选择交付状态", "customerConfirmed": "客户确认", "customerConfirmedPlaceholder": "请选择确认状态", "confirmationType": "确认方式", "confirmationTypePlaceholder": "请选择确认方式", "deliveryTime": "交付时间", "customerConfirmTime": "客户确认时间", "startDate": "开始日期", "endDate": "结束日期", "customerConfirmTimeStartPlaceholder": "确认开始日期", "customerConfirmTimeEndPlaceholder": "确认结束日期", "invoiceTime": "开票时间", "actualDeliveryDate": "实际交付日期", "deliveryNotes": "交付备注", "signaturePhoto": "签字照片", "submitConfirm": "提交确认", "deliveryConfirm": "交车确认", "statusPending": "待交付", "statusConfirming": "待确认", "statusCompleted": "已完成", "orderStatusNormal": "正常", "orderStatusCancelled": "已取消", "orderStatusPendingAllocation": "待分配", "orderStatusAllocating": "分配中", "orderStatusAllocated": "已分配", "orderStatusPendingDelivery": "待交付", "orderStatusDelivered": "已交付", "confirmationTypeApp": "APP确认", "confirmationTypeOffline": "线下确认", "fetchDataFailed": "获取数据失败", "detailNotFound": "详情数据未找到", "fetchDetailFailed": "获取详情失败", "printFeatureNotImplemented": "打印功能待实现", "submitConfirmSuccess": "提交确认成功", "submitConfirmFailed": "提交确认失败", "deliveryConfirmSuccess": "交车确认成功", "deliveryConfirmFailed": "交车确认失败", "exportSuccess": "导出成功", "exportFailed": "导出失败", "submitConfirmTitle": "提交确认", "submitConfirmQuestion": "确认要提交此交付单吗？", "deliveryNumberLabel": "交车单号", "orderNumberLabel": "订单编号", "customerNameLabel": "客户姓名", "customerPhoneLabel": "客户电话", "submitConfirmNote": "提交后状态将会变更", "statusPendingDelivery": "待交付", "statusPendingConfirm": "待确认", "confirmSubmit": "确认提交", "noOrderSelected": "未选择订单数据", "deliveryConfirmTitle": "交车确认", "orderInfoTitle": "订单信息", "modelLabel": "车型", "variantLabel": "配置", "colorLabel": "颜色", "deliveryInfoTitle": "交车信息", "deliveryTimeLabel": "交车时间", "deliveryTimeRequired": "请选择交车时间", "deliveryTimePlaceholder": "请选择交车时间", "customerSignatureConfirm": "客户签字确认", "uploadSignaturePhoto": "上传签字照片", "uploadSignaturePhotoTip": "支持JPG、PNG格式，文件大小不超过5MB", "deliveryNotesLabel": "交车备注", "deliveryNotesPlaceholder": "请输入交车备注（可选）", "signaturePhotoFormatError": "签字照片格式错误，请上传JPG或PNG格式", "signaturePhotoSizeError": "签字照片大小不能超过5MB", "signaturePhotoRequired": "请上传客户签字照片", "signatureUploadFailed": "签字照片上传失败", "detailTitle": "交付详情", "basicInfo": "基本信息", "orderCreatorName": "下单人姓名", "orderCreatorPhone": "下单人电话", "customerType": "客户类型", "idType": "证件类型", "idNumber": "证件号码", "address": "地址", "city": "城市", "postcode": "邮编", "state": "州/省", "orderInfo": "订单信息", "orderCreateTime": "订单创建时间", "orderPaymentStatus": "支付状态", "paymentMethod": "支付方式", "vehicleInfo": "车辆信息", "warehouseName": "仓库名称", "productionDate": "生产日期", "entryTime": "入库时间", "deliveryInfo": "交车信息", "noDetailData": "暂无详情数据", "statusProcessing": "处理中", "statusDelivered": "已交付", "statusCancelled": "已取消", "orderStatusPending": "待处理", "orderStatusConfirmed": "已确认", "confirmationTypeOnline": "在线确认", "confirmationTypePhone": "电话确认", "exportSettingsTitle": "导出设置", "exportFormat": "导出格式", "exportFormatRequired": "请选择导出格式", "exportRange": "导出范围", "exportRangeRequired": "请选择导出范围", "exportRangeCurrentPage": "当前页数据", "exportRangeFilteredResult": "筛选结果", "exportRangeAllData": "全部数据", "exportTimeRange": "时间范围"}, "vehicleQuery": {"title": "车辆查询", "vin": "车架号", "vinPlaceholder": "请输入车架号", "factoryOrderNo": "工厂订单号", "factoryOrderNoPlaceholder": "请输入工厂订单号", "warehouseName": "仓库名称", "model": "车型", "variant": "配置", "color": "颜色", "fmrId": "FMRID", "fmrIdPlaceholder": "请输入FMRID", "lockStatus": "锁定状态", "invoiceStatus": "开票状态", "deliveryStatus": "交车状态", "stockStatus": "库存状态", "invoiceDate": "开票日期", "deliveryDate": "交车日期", "storageDate": "入库日期", "productionDate": "生产日期", "detailDialogTitle": "车辆详情", "exportDialogTitle": "导出数据", "exportFormat": "导出格式", "exportFormatPlaceholder": "请选择导出格式", "excel": "Excel", "csv": "CSV", "exportScope": "导出范围", "exportCurrentPage": "当前页数据", "exportAllData": "全部数据", "exportSuccess": "导出{format}文件成功！", "fetchConfigFailed": "获取车型配置失败"}, "testDriveReport": {"title": "试驾报表", "monthlyCount": "本月试驾次数", "dailyCount": "今日试驾次数", "topStore": "试驾次数最多的门店", "topModel": "试驾量最多的车型", "store": "门店", "model": "试驾车型", "variant": "试驾配置", "testDriveTime": "试驾时间", "recordList": "试驾记录列表", "testDriveNo": "试驾单号", "createTime": "试驾单录入时间", "customerName": "试驾人", "customerPhone": "试驾人手机号", "mileage": "里程数", "startTime": "试驾开始时间", "endTime": "试驾结束时间", "consultantName": "销售顾问", "detailTitle": "试驾单详情", "customerInfo": "潜客信息", "customerSource": "潜客来源", "idType": "身份证件类别", "idNumber": "潜客证件号", "email": "潜客邮箱", "testDriveInfo": "试驾信息", "driverName": "试驾人", "driverPhone": "试驾人手机号", "driverIdType": "试驾人证件类型", "driverIdNumber": "试驾人证件号", "vehicleInfo": "试驾车型", "startMileage": "开始里程数", "endMileage": "结束里程数", "feedback": "试驾反馈"}, "factoryOrderManagement": {"title": "工厂订单管理", "monthlyOrderCount": "本月订单数量", "dailyOrderCount": "今日订单数量", "monthlyGrowthRate": "月增长率", "dailyGrowthRate": "日增长率", "topDealer": "订单最多门店", "topVehicle": "最热销车型", "dealerName": "经销商名称", "model": "车型", "variant": "配置", "orderStatus": "订单状态", "paymentStatus": "支付状态", "orderDate": "订单日期", "orderNumber": "订单号", "listTitle": "订单列表", "exportExcel": "导出Excel", "creationTime": "创建时间", "customerName": "客户姓名", "customerPhone": "客户电话", "customerType": "客户类型", "vin": "车架号", "paymentMethod": "支付方式", "loanStatus": "贷款状态", "approvalStatus": "审批状态", "insuranceStatus": "保险状态", "jpjRegistrationStatus": "JPJ注册状态", "orderDetailTitle": "订单详情", "basicInfo": "基本信息", "orderInfo": "订单信息", "customerInfo": "客户信息", "vehicleInfo": "车辆信息", "paymentInfo": "支付信息", "loanInfo": "贷款信息", "insuranceInfo": "保险信息", "jpjInfo": "JPJ注册信息", "customerEmail": "客户邮箱", "engineNumber": "发动机号", "chassisNumber": "底盘号", "totalAmount": "总金额", "paidAmount": "已付金额", "remainingAmount": "剩余金额", "loanAmount": "贷款金额", "loanBank": "贷款银行", "approvalDate": "审批日期", "insuranceCompany": "保险公司", "insuranceType": "保险类型", "insuranceAmount": "保险金额", "registrationNumber": "注册号码", "registrationDate": "注册日期", "personalDetails": "个人详情", "preferredOutletSalesAdvisor": "首选门店销售顾问", "purchaseDetails": "购车详情", "vehicleInfoTab": "车辆信息", "invoiceInfoTab": "开票信息", "serviceRightsTab": "服务&权益信息", "paymentInfoTab": "付款信息", "insuranceInfoTab": "保险信息", "otrFeesTab": "OTR费用信息", "accessoriesInfo": "配件信息", "accessoryName": "配件名称", "quantity": "数量", "unitPrice": "单价", "totalPrice": "总价", "accessoriesTotalAmount": "配件总金额", "invoiceInfo": "开票信息", "invoiceNumber": "发票号码", "invoiceDate": "开票日期", "invoiceAmount": "开票金额", "invoiceStatus": "开票状态", "invoiceCompany": "开票公司", "invoiceAddress": "开票地址", "serviceRightsInfo": "服务&权益信息", "serviceName": "服务名称", "serviceType": "服务类型", "servicePrice": "服务价格", "validityPeriod": "有效期", "serviceStatus": "服务状态", "servicesTotalAmount": "服务总金额", "totalInvoicePrice": "车辆开票价", "downPaymentAmount": "首付金额", "balanceAmount": "尾款金额", "otrFeesInfo": "OTR费用信息", "ticketNumber": "票据号码", "feeItem": "费用项目", "feePrice": "费用价格", "otrFeesTotalAmount": "OTR费用总金额", "policyNumber": "保单号码", "insurancePrice": "保险价格", "insurancesTotalAmount": "保险总金额", "insuranceNotes": "保险备注", "changeRecordsInfo": "变更记录信息", "changeRecordsTab": "变更记录", "originalContent": "原始内容", "changedContent": "变更内容", "operator": "操作人", "operationTime": "操作时间", "vehicleInvoicePrice": "车辆开票价", "remainingReceivable": "剩余应收", "backToList": "返回列表", "ordererNameField": "下单人姓名", "ordererPhoneField": "下单人电话", "buyerNameField": "购车人姓名", "buyerPhoneField": "购车人电话", "buyerIdType": "证件类型", "buyerIdNumber": "证件号码", "buyerEmail": "邮箱", "buyerAddress": "地址", "buyerState": "州", "buyerCity": "城市", "buyerPostcode": "邮编", "region": "区域", "dealerCity": "经销商城市", "salesConsultant": "销售顾问", "storeInfo": "门店信息", "purchaseInfo": "购车信息", "vehiclePrice": "车辆价格", "deliveryDate": "交付日期", "effectiveDateField": "生效日期", "expirationDateField": "到期日期"}, "invoiceManagement": {"title": "发票管理", "invoiceDetail": "发票详情", "emailConfirm": "邮件发送确认", "confirmSendEmail": "确认发送发票邮件到客户邮箱？", "companyInfo": "公司信息", "state": "州", "city": "城市", "postcode": "邮编", "financeInfo": "金融信息", "financeType": "金融方式", "priceStructureDetails": "价格结构明细", "optionalAccessories": "选配件", "category": "规格分类", "totalPrice": "总价", "totalAccessoryAmount": "选配件总金额", "subtotal": "小计", "otrRegistrationFees": "OTR登记费用", "billNumber": "单据编号", "feeItem": "费用项目", "feePrice": "费用价格", "totalOtrFeeAmount": "OTR费用总金额", "insurancePremium": "保险费用", "totalSalesPrice": "总销售价", "businessType": "业务类型", "serialNumber": "流水号", "channel": "渠道", "amount": "总价", "remark": "备注", "invoiceNumber": "发票编号", "invoiceNumberPlaceholder": "请输入发票编号", "invoiceDate": "开票日期", "orderNumber": "订单编号", "orderNumberPlaceholder": "请输入订单编号", "customerName": "购车人姓名", "customerNamePlaceholder": "请输入购车人姓名", "customerPhone": "购车人手机号", "customerPhonePlaceholder": "请输入购车人手机号", "customerEmail": "购车人邮箱", "customerEmailPlaceholder": "请输入购车人邮箱", "customerAddress": "购车人地址", "customerState": "州", "customerCity": "城市", "customerPostcode": "邮编", "deliveryNumber": "交车单编号", "salesConsultantId": "销售顾问ID", "contactPhone": "联系电话", "contactEmail": "联系邮箱", "companyAddress": "公司地址", "vin": "VIN码", "vinPlaceholder": "请输入VIN码", "model": "车型", "variant": "变型", "color": "颜色", "salesStore": "销售门店", "salesConsultant": "销售顾问", "paymentMethod": "付款方式", "financeCompany": "金融公司", "loanAmount": "贷款金额", "invoiceAmount": "发票总额", "createdTime": "创建时间", "salesType": "销售类型", "export": "导出", "batchPrint": "批量打印", "detail": "详情", "print": "打印", "email": "邮件", "log": "日志", "pleaseSelectRecords": "请选择要操作的记录", "printSuccess": "打印成功", "printFailed": "打印失败", "batchPrintSuccess": "批量打印成功", "batchPrintFailed": "批量打印失败", "emailSentSuccess": "邮件发送成功", "emailSentFailed": "邮件发送失败", "exportSuccess": "导出成功", "exportFailed": "导出失败", "detailTitle": "发票详情", "basicInfo": "基本信息", "customerInfo": "客户信息", "vehicleInfo": "车辆信息", "salesInfo": "销售信息", "priceDetails": "价格明细", "accessoryDetails": "选配件明细", "receiptDetails": "收据明细", "invoiceCompany": "开票公司", "gstNumber": "GST编号", "sstNumber": "SST编号", "engineNumber": "发动机号", "chassisNumber": "底盘号", "tinNumber": "TIN编号", "modelCode": "车型代码", "modelDescription": "车型描述", "engineDisplacement": "发动机排量", "vehicleRegistrationDate": "车辆登记日期", "insuranceInfo": "保险信息", "insuranceCompany": "保险公司", "agentCode": "代理编码", "policyNumber": "保单号", "issueDate": "出单日期", "insuranceAmount": "保险费用", "licensePlateFee": "车牌费用", "otrFeeDetails": "OTR费用明细", "feeCode": "费用编码", "feeType": "费用类型", "taxAmount": "费用金额", "effectiveDate": "生效日期", "expiryDate": "失效日期", "receiptNo": "流水号", "collectionType": "收款类型", "loanPeriod": "贷款期限", "vehicleSalesPrice": "车辆销售价", "accessoryAmount": "选配件总金额", "otrAmount": "OTR费用总金额", "adjustmentAmount": "调整金额", "invoiceNetValue": "发票净值", "specification": "规格分类", "accessoryName": "配件名称", "unitPrice": "单价", "quantity": "数量", "receiptNumber": "收据编号", "receiptType": "业务类型", "paymentChannel": "渠道", "paidAmount": "金额", "arrivalTime": "到账时间", "remarks": "备注", "emailTitle": "发送发票邮件", "recipientEmail": "收件人邮箱", "emailPlaceholder": "请输入邮箱地址", "emailSubject": "邮件主题", "emailContent": "邮件内容", "emailContentPlaceholder": "请输入邮件内容", "emailRequired": "请输入邮箱地址", "emailFormatError": "邮箱格式不正确", "subjectRequired": "请输入邮件主题", "contentRequired": "请输入邮件内容", "defaultEmailSubject": "发票 {invoiceNumber} - <PERSON><PERSON><PERSON>", "defaultEmailContent": "尊敬的 {customerName}，\n\n您好！\n\n感谢您购买Perodua车辆。请查收您的发票信息：\n\n发票编号：{invoiceNumber}\n开票日期：{invoiceDate}\n发票金额：{invoiceAmount}\n\n如有任何疑问，请随时联系我们。\n\n谢谢！\nPerodua销售团队", "exportTitle": "导出配置", "exportFormat": "导出格式", "exportScope": "导出范围", "exportFields": "导出字段", "currentPage": "当前页数据", "filteredData": "筛选后数据", "allData": "全部数据", "selectAll": "全选", "clearAll": "清空", "startExport": "开始导出", "formatRequired": "请选择导出格式", "scopeRequired": "请选择导出范围", "fieldsRequired": "请至少选择一个导出字段", "logTitle": "操作日志", "operationType": "操作类型", "operator": "操作人", "operationTime": "操作时间", "operationDescription": "操作描述", "operationResult": "操作结果", "errorMessage": "错误信息", "noLogData": "暂无日志数据", "viewDetail": "查看详情", "emailSend": "发送邮件", "exportData": "导出数据", "create": "创建", "update": "更新", "delete": "删除", "success": "成功", "failed": "失败", "pending": "处理中"}, "vehicleModel": {"title": "车型主数据管理", "model": "车型", "variantName": "版本名称", "variantCode": "版本代码", "colourName": "颜色名称", "colourCode": "颜色代码", "fmrid": "FMRID", "createTime": "创建时间", "updateTime": "更新时间", "syncData": "同步数据", "syncLog": "同步日志", "exportData": "导出数据", "syncSuccess": "数据同步成功", "exportSuccess": "数据导出成功", "fmridPlaceholder": "请输入FMRID", "syncLogTitle": "数据同步日志"}, "factoryProspect": {"title": "厂端潜客池管理", "storeUnit": "家门店", "notSet": "未设置", "notConverted": "未成交", "statistics": {"totalLeadCount": "线索总数", "hLevelProspectCount": "H级门店潜客数", "monthlyDealProspectCount": "本月成交", "crossStoreCustomerCount": "跨门店客户数", "vsLastMonth": "较上月", "conversionRate": "成交率", "ratio": "占比"}, "search": {"leadId": "潜客ID", "inputLeadId": "请输入潜客ID", "store": "门店", "allStores": "全部门店", "storeCount": "关联门店数", "allStoreCount": "全部", "singleStore": "单店", "multiStore": "多店", "registrationTime": "注册时间", "search": "查询", "reset": "重置", "export": "导出数据"}, "viewType": {"all": "全部潜客", "crossStore": "跨门店潜客", "noIntention": "无意向潜客", "converted": "已转化"}, "table": {"index": "序号", "leadId": "潜客ID", "customerName": "潜客姓名", "phoneNumber": "手机号", "associatedStoreCount": "关联门店数", "registrationTime": "注册时间", "prospectStatus": "潜客状态", "actions": "操作", "detail": "详情"}, "messages": {"resetSuccess": "筛选条件已重置", "exportStart": "正在导出数据，请稍候...", "exportSuccess": "数据导出成功", "exportFailed": "导出数据失败", "fetchStatsFailed": "获取统计数据失败", "fetchListFailed": "获取潜客列表失败"}, "common": {"all": "全部", "noData": "暂无", "notSet": "未设置", "notConverted": "未成交", "storeUnit": "家门店", "exporting": "正在导出数据，请稍候...", "resetSuccess": "筛选条件已重置", "exportSuccess": "数据导出成功", "exportFailed": "导出数据失败", "fetchStatsFailed": "获取统计数据失败", "fetchDataFailed": "获取数据失败"}, "customerDetail": {"title": "潜客详情", "close": "关闭", "tabs": {"basic": "基本信息", "stores": "门店关联", "followup": "跟进记录", "testdrive": "试驾记录", "failed": "无意向记录", "history": "变更历史", "analytics": "效果分析"}, "basicInfo": {"customerId": "客户ID", "customerName": "客户姓名", "phoneNumber": "手机号", "idType": "证件类型", "idNumber": "证件号码", "email": "邮箱", "registerTime": "注册时间", "registerSource": "注册来源", "currentStatus": "当前状态"}, "storeAssociation": {"title": "关联门店记录", "storeCount": "共{count}家门店", "associationTime": "关联时间", "associationReason": "关联原因", "currentAdvisor": "当前顾问", "lastFollowUp": "最后跟进", "noFollowUp": "暂无跟进", "level": "级"}, "followUpTable": {"store": "门店", "advisor": "销售顾问", "method": "跟进方式", "time": "跟进时间", "intentLevel": "意向级别", "details": "跟进情况"}, "testDriveTable": {"store": "门店", "driver": "试驾人", "phone": "手机号", "model": "试驾车型", "variant": "试驾配置", "time": "试驾时间", "feedback": "客户反馈"}, "defeatTable": {"markTime": "标记时间", "store": "门店", "marker": "标记人", "reason": "无意向原因"}, "historyTable": {"changeTime": "变更时间", "store": "门店", "changeType": "变更类型", "operator": "操作人", "oldValue": "变更前", "newValue": "变更后"}, "analytics": {"totalFollowUp": "跟进总次数", "mostActiveStore": "最活跃门店", "crossStores": "跨{count}家门店", "noStoreData": "暂无门店数据", "noStore": "无", "followUpTimes": "跟进{count}次", "noFollowUp": "暂无跟进"}, "messages": {"fetchDetailFailed": "获取客户详情失败"}}}, "vehicleRegistration": {"title": "车辆登记管理", "pageTitle": "车辆登记管理", "search": {"orderNumber": "订单号", "customerName": "客户姓名", "customerPhone": "客户电话", "registrationStatus": "登记状态", "vin": "车架号", "insuranceStatus": "保险状态", "salesAdvisor": "销售顾问", "pushTimeRange": "推送时间范围", "pushTimeStart": "推送开始时间", "pushTimeEnd": "推送结束时间", "searchButton": "搜索", "resetButton": "重置", "exportButton": "导出", "batchPushButton": "批量推送", "refreshButton": "刷新", "placeholder": {"orderNumber": "请输入订单号", "customerName": "请输入客户姓名", "customerPhone": "请输入客户电话", "vin": "请输入车架号", "registrationStatus": "请选择登记状态", "insuranceStatus": "请选择保险状态", "salesAdvisor": "请选择销售顾问"}}, "table": {"orderNumber": "订单号", "customerName": "客户姓名", "customerPhone": "客户电话", "vin": "车架号", "vehicleModel": "车型", "vehicleColor": "车身颜色", "insuranceStatus": "保险状态", "companyName": "保险公司", "registrationStatus": "登记状态", "lastPushTime": "最后推送时间", "registrationFee": "登记费用", "salesAdvisor": "销售顾问", "createdAt": "创建时间", "actions": "操作", "selectAll": "全选", "selectedCount": "已选择 {count} 项", "totalCount": "共 {total} 条记录", "serialNumber": "序号"}, "actions": {"viewDetail": "查看详情", "push": "推送", "retryPush": "重新推送", "batchPush": "批量推送", "export": "导出", "refresh": "刷新"}, "status": {"pending": "待推送", "processing": "推送中", "success": "推送成功", "failed": "推送失败", "all": "全部状态"}, "insurance": {"insured": "已投保", "not_insured": "未投保", "all": "全部状态"}, "dialog": {"detail": {"title": "车辆登记详情", "orderInfo": "订单信息", "customerInfo": "客户信息", "vehicleInfo": "车辆信息", "insuranceInfo": "保险信息", "jpjInfo": "JPJ登记信息", "feeDetails": "费用明细", "operationLogs": "操作日志", "closeButton": "关闭"}, "pushConfirm": {"title": "推送确认", "content": "确定要推送选中的车辆登记信息到JPJ系统吗？", "singleContent": "确定要推送该车辆登记信息到JPJ系统吗？", "batchContent": "确定要批量推送选中的 {count} 条车辆登记信息到JPJ系统吗？", "confirmButton": "确定推送", "cancelButton": "取消", "selectedRecords": "选中的记录", "moreRecords": "还有 {count} 条记录..."}, "retryPush": {"title": "重新推送确认", "content": "该登记之前推送失败，确定要重新推送到JPJ系统吗？", "confirmButton": "确定重新推送", "cancelButton": "取消", "failureReason": "失败原因", "recordInfo": "记录信息"}}, "fields": {"orderInfo": {"orderNumber": "订单号", "orderStatus": "订单状态", "paymentStatus": "支付状态", "lastPushTime": "最后推送时间", "createdAt": "创建时间", "salesAdvisor": "销售顾问", "pushCount": "推送次数"}, "customerInfo": {"name": "客户姓名", "idType": "证件类型", "idNumber": "证件号码", "phone": "联系电话", "email": "邮箱地址", "type": "客户类型", "address": "详细地址", "city": "城市", "postcode": "邮编", "state": "州/省"}, "vehicleInfo": {"vin": "车架号", "model": "车型", "variant": "配置", "color": "颜色", "engineNumber": "发动机号", "modelCode": "车型代码", "productionYear": "生产年份", "manufactureDate": "制造日期", "otrAmount": "OTR价格", "totalAmount": "总价"}, "insuranceInfo": {"status": "保险状态", "company": "保险公司", "number": "保险号码", "policyNumber": "保单号", "period": "保险期间", "startDate": "保险开始日期", "endDate": "保险结束日期", "date": "投保日期", "fee": "保险费用"}, "jpjInfo": {"status": "登记状态", "certificateNumber": "登记证书号", "jpjCertificateNumber": "JPJ证书号", "jpjReferenceNumber": "JPJ参考号", "pushTime": "推送时间", "completionTime": "完成时间", "registrationFee": "登记费用", "operator": "操作员", "failureReason": "失败原因"}, "operationLog": {"operationTime": "操作时间", "operationType": "操作类型", "operatorName": "操作员", "operationSource": "操作来源", "result": "结果", "statusChange": "状态变更", "remark": "备注"}, "feeDetails": {"feeType": "费用类型", "amount": "金额", "totalAmount": "费用总计", "createdAt": "创建时间"}}, "messages": {"success": {"pushSuccess": "推送成功", "retryPushSuccess": "重新推送成功", "batchPushSuccess": "批量推送成功", "exportSuccess": "导出成功", "refreshSuccess": "刷新成功"}, "error": {"pushFailed": "推送失败，请重试", "retryPushFailed": "重新推送失败，请重试", "batchPushFailed": "批量推送失败，请重试", "exportFailed": "导出失败，请重试", "loadFailed": "数据加载失败，请重试", "detailLoadFailed": "详情加载失败，请重试", "noSelection": "请先选择要操作的记录", "networkError": "网络错误，请检查网络连接"}, "warning": {"noData": "暂无数据", "confirmOperation": "请确认操作", "unsavedChanges": "有未保存的更改", "noPushableItems": "选中的记录中没有可推送的项目"}, "info": {"loading": "加载中...", "pushing": "推送中...", "exporting": "导出中...", "processing": "处理中..."}}, "units": {"currency": "RM", "count": "项", "page": "页"}}}