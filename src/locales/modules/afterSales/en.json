{"appointments": {"appointmentManagement": "Appointment Management", "exportExcel": "Export Excel", "createInspection": "Create Inspection", "appointmentDetail": "Appointment Detail", "labels": {"appointmentId": "Appointment ID", "licensePlate": "License Plate", "reservationPhone": "Reservation Phone", "servicePhone": "Service Phone", "creationTime": "Creation Time", "status": "Status", "jobType": "Job Type", "serviceAdvisor": "Service Advisor", "unassigned": "Unassigned", "notGenerated": "Not Generated", "totalRecords": "Total {total} records", "appointmentTime": "Appointment Time", "customerDescription": "Customer Description", "reservationContactName": "Reservation Contact", "reservationContactPhone": "Reservation Phone", "serviceContactName": "Service Contact", "serviceContactPhone": "Service Phone", "vin": "VIN", "model": "Model", "variant": "<PERSON><PERSON><PERSON>", "color": "Color", "mileage": "Mileage", "vehicleAge": "Vehicle Age", "store": "Service Store", "totalAmount": "Total Amount", "paymentStatus": "Payment Status", "paymentAmount": "Payment Amount", "paymentOrderNumber": "Payment Order Number", "packageName": "Maintenance Package", "packageCode": "Package Code", "estimatedTotal": "Total Price", "paymentMethod": "Payment Method"}, "placeholders": {"appointmentId": "Enter Appointment ID", "licensePlate": "Enter License Plate", "reservationPhone": "Enter Reservation Phone", "servicePhone": "Enter Service Phone", "selectStatus": "Select Status", "selectJobType": "Select job type", "selectServiceAdvisor": "Select service advisor", "serviceContactName": "Enter Service Contact Name", "serviceContactPhone": "Enter Service Contact Phone"}, "headers": {"appointmentId": "Appointment ID", "licensePlate": "License Plate", "reservationContactName": "Reservation Contact", "reservationContactPhone": "Reservation Phone", "serviceContactName": "Service Contact", "serviceContactPhone": "Service Phone", "appointmentDate": "Appointment Date", "timeSlot": "Time Slot", "jobType": "Job Type", "status": "Status", "serviceAdvisor": "Service Advisor", "qualityInspectionId": "Quality Inspection ID", "creationTime": "Creation Time", "servicePackageCode": "Service Code", "servicePackageName": "Service Name", "quantity": "Quantity", "price": "Price", "itemName": "Item Name", "itemCode": "Item Code", "standardHours": "Standard Hours", "unitPrice": "Unit Price", "subtotal": "Subtotal", "partsName": "Parts Name", "partsCode": "Parts Code", "unit": "Unit"}, "statuses": {"arrived": "Arrived", "not_arrived": "Not Arrived", "cancelled": "Cancelled", "pending_payment": "Pending Payment"}, "jobTypes": {"maintenance": "Maintenance", "repair": "Repair"}, "paymentStatuses": {"paid": "Paid", "unpaid": "Unpaid", "refunded": "Refunded"}, "titles": {"appointmentInfo": "Appointment Information", "customerInfo": "Customer Information", "vehicleInfo": "Vehicle Information", "appointmentBookingInfo": "Appointment Information", "serviceContent": "Service Content", "paymentInfo": "Payment Information", "confirmCreateInspection": "Confirm Create Inspection", "storeInfo": "Store Information", "operationHistory": "Operation History", "laborDetails": "Labor Details", "partsDetails": "Parts Details"}, "paymentMethods": {"online": "Online Payment", "store": "Pay at Store"}, "buttons": {"confirmCreation": "Confirm Creation"}, "messages": {"inspectionCreatedSuccess": "Inspection created successfully!", "inspectionCreatedFailed": "Failed to create inspection.", "confirmCreateInspection": "You are creating an inspection for this appointment. Please confirm the service contact information."}, "mock": {"maintenancePackageName": "Basic Maintenance Package", "oilFilter": "Oil Filter"}}, "dashboard": {"pageTitle": "Appointment Dashboard", "stats": {"totalAppointments": "Total Appointments Today", "arrivedCount": "Arrived Count", "notArrivedCount": "Not Arrived Count", "arrivalRate": "Arrival Rate"}, "filters": {"all": "All", "notArrived": "Not Arrived", "tomorrow": "Tomorrow's Appointments"}, "table": {"plateNumber": "License Plate", "appointmentDate": "Appointment Date", "timeSlot": "Time Slot", "serviceType": "Service Type", "status": "Status"}, "serviceTypes": {"maintenance": "Maintenance", "repair": "Repair"}, "status": {"arrived": "Arrived", "notArrived": "Not Arrived", "notFulfilled": "Not Fulfilled"}, "emptyState": "No appointment data"}, "appointmentDashboard": {"pageTitle": "Appointment Dashboard", "stats": {"totalAppointments": "Total Appointments Today", "arrivedCount": "Arrived Count", "notArrivedCount": "Not Arrived Count", "arrivalRate": "Arrival Rate"}, "filters": {"all": "All", "notArrived": "Not Arrived", "tomorrow": "Tomorrow's Appointments"}, "table": {"plateNumber": "License Plate", "appointmentDate": "Appointment Date", "timeSlot": "Time Slot", "serviceType": "Service Type", "status": "Status"}, "serviceTypes": {"maintenance": "Maintenance", "repair": "Repair"}, "status": {"arrived": "Arrived", "notArrived": "Not Arrived", "notFulfilled": "Not Fulfilled"}, "emptyState": "No appointment data"}, "statuses": {"arrived": "Arrived", "not_arrived": "Not Arrived", "cancelled": "Cancelled", "pending_payment": "Pending Payment"}, "jobTypes": {"maintenance": "Maintenance", "repair": "Repair"}, "paymentStatuses": {"paid": "Paid", "unpaid": "Unpaid", "refunded": "Refunded"}, "messages": {"inspectionCreatedSuccess": "Inspection created successfully!", "inspectionCreatedFailed": "Failed to create inspection.", "confirmCreateInspection": "You are creating an inspection for this appointment, please confirm the service contact information."}, "titles": {"appointmentInfo": "Appointment Information", "customerInfo": "Customer Information", "vehicleInfo": "Vehicle Information", "appointmentBookingInfo": "Appointment Information", "serviceContent": "Service Content", "paymentInfo": "Payment Information", "confirmCreateInspection": "Confirm Create Inspection"}, "buttons": {"confirmCreation": "Confirm Creation"}, "mock": {"maintenancePackageName": "Basic Maintenance Package", "oilFilter": "Oil Filter"}, "checkin": {"checkinList": "Store Check-in", "checkinId": "Check-in ID", "checkinIdPlaceholder": "Enter Check-in ID", "licensePlate": "License Plate", "licensePlatePlaceholder": "Enter License Plate", "repairPersonName": "Repair Person Name", "repairPersonNamePlaceholder": "Enter Repair Person Name", "repairPersonPhone": "Repair Person Phone", "repairPersonPhonePlaceholder": "Enter Repair Person Phone", "createdAt": "Created At", "createRecord": "Create Record", "export": "Export", "id": "ID", "vin": "VIN", "vehicleModel": "Vehicle Model", "vehicleConfiguration": "Vehicle Configuration", "color": "Color", "mileage": "Mileage", "mileageUnit": "km", "serviceAdvisor": "Service Advisor", "relatedRepairOrderId": "Related Repair Order ID", "serviceType": "Service Type", "updatedAt": "Updated At", "viewDetails": "View Details", "createRepairOrder": "Create Repair Order", "editCheckinRecord": "Edit Check-in Record", "addCheckinRecord": "Add Check-in Record", "vehicleInfoAutoFill": "Vehicle info can be auto-filled by entering license plate", "vehicleInfo": "Vehicle Information", "mileagePlaceholder": "Enter Mileage", "vehicleAge": "Vehicle Age", "customerInfo": "Customer Information", "notes": "Notes", "notesPlaceholder": "Enter Notes", "repairOrderAlreadyExists": "This check-in record already has a related repair order.", "confirmCreateRepairOrder": "Are you sure you want to create a repair order for check-in ID {checkinId}?", "repairOrderCreatedSuccess": "Repair order created successfully!", "vehicleInfoNotFound": "No vehicle information found for this license plate."}, "quota": {"pageTitle": "Appointment Quota Management", "storeName": "Store Name", "storeCode": "Store Code", "permissionTip": "You can currently only manage appointment quota configurations for this store", "configuredListTitle": "Configured Quota List", "addNewQuota": "Add New Quota", "emptyState": "No configuration data available, click the button above to start configuring", "table": {"configDate": "Configuration Date", "timeSlotCount": "Time Slot Count", "totalQuota": "Total Quota", "bookedQuantity": "Booked Quantity", "lastUpdateTime": "Last Update Time"}, "modal": {"title": "Add New Quota", "editTitle": "<PERSON>", "selectDate": "Select Date", "dateLabel": "Configuration Date", "datePlaceholder": "Please select configuration date", "dateTip": "Only today and future dates can be configured", "existingConfig": "This date already has configuration", "timeSlotConfiguration": "Time Slot Configuration", "addTimeSlot": "Add Time Slot", "noTimeSlots": "No time slot configuration", "clickAddPrompt": "Click the button in the upper right corner to add time slots", "timeSlot": "Time Slot", "startTime": "Start Time", "endTime": "End Time", "quota": "<PERSON><PERSON><PERSON>", "startTimePlaceholder": "Select start time", "endTimePlaceholder": "Select end time", "quotaPlaceholder": "Enter quota quantity", "configDescriptionTitle": "Configuration Instructions", "configDescription": {"item1": "Business hours: 08:00-18:00, please configure time slots within this range", "item2": "Time slots cannot overlap, end time must be later than start time", "item3": "Quota quantity must be a positive integer", "item4": "Takes effect immediately after saving, customers can make appointments for corresponding time slots"}}, "summary": {"selectDate": "Please select configuration date first", "addTimeSlot": "Please add at least one time slot", "timeSlotsUnit": " time slots", "totalQuota": "Total Quota: "}, "validation": {"dateRequired": "Please select configuration date", "atLeastOneTimeSlot": "Please add at least one time slot", "timeRequired": "Please select start time and end time", "startBeforeEnd": "Start time must be earlier than end time", "quotaPositive": "Quota quantity must be greater than 0"}, "messages": {"dateMustBeFutureOrToday": "Configuration date cannot be earlier than today", "selectDateFirst": "Please select configuration date first", "timeSlotExceedsOperatingHours": "Time slot cannot exceed business hours (18:00)", "unsavedChangesWarning": "You have unsaved changes, are you sure you want to close?"}}, "quotaManagement": {"pageTitle": "Appointment Quota Management", "storeName": "Beijing Chaoyang Store", "storeCode": "BJ001", "permissionTip": "You have permission to manage appointment quotas for this store", "configuredListTitle": "Configured Quota List", "addNewQuota": "Add New Appointment Quota", "editQuotaTitle": "Edit Appointment Quota", "addNewQuotaTitle": "Add New Appointment Quota", "emptyState": "No configuration data, click the button above to start configuration", "unsavedChangesWarning": "You have unsaved changes, are you sure you want to leave?", "dateMustBeFutureOrToday": "Date must be today or future date", "selectDateFirst": "Please select a date first", "timeSlotExceedsOperatingHours": "Time slot cannot exceed operating hours (18:00)", "table": {"configDate": "Config Date", "timeSlotCount": "Time Slot Count", "totalQuota": "Total Quota", "bookedQuantity": "Booked Quantity", "lastUpdateTime": "Last Update Time"}, "modal": {"title": "Add New Appointment Quota", "editTitle": "Edit Appointment Quota", "selectDate": "Select Date", "selectDateTitle": "Select Date", "dateLabel": "Date", "datePlaceholder": "Please select appointment date", "dateTip": "Select the date for which you want to configure appointment quotas.", "existingConfig": "This date already has a configuration. Modifying will overwrite the original configuration.", "timeSlotConfiguration": "Time Slot Configuration", "timeSlotConfigTitle": "Time Slot Configuration", "addTimeSlot": "Add Time Slot", "noTimeSlots": "No time slot configuration", "clickAddPrompt": "Click the 'Add Time Slot' button in the upper right corner to start configuration", "timeSlot": "Time Slot", "startTime": "Start Time", "startTimePlaceholder": "Please select start time", "endTime": "End Time", "endTimePlaceholder": "Please select end time", "quota": "<PERSON><PERSON><PERSON>", "quotaPlaceholder": "Please enter quota number", "configDescriptionTitle": "Configuration Instructions", "configDescription": {"item1": "Operating hours: 08:00-18:00, time slots can only be configured within this time range", "item2": "Time slots cannot overlap, the system will automatically check for time slot conflicts", "item3": "The quota number for each time slot must be greater than 0", "item4": "Takes effect immediately after saving, customers can book corresponding time slots"}}, "summary": {"selectDate": "Please select a date first", "addTimeSlot": "Please add time slot configuration", "timeSlotsUnit": " time slots", "totalQuota": "Total Quota: "}, "validation": {"dateRequired": "Please select appointment date", "atLeastOneTimeSlot": "At least one time slot needs to be configured", "timeRequired": "Please complete the start and end times for the time slot", "startBeforeEnd": "Start time must be earlier than end time", "quotaPositive": "Quota number must be greater than 0", "timeSlotOverlap": "Time slot {slot1} overlaps with {slot2}, please adjust"}}, "inspection": {"title": "Inspection Management", "searchForm": {"inspectionNo": "Inspection No", "inspectionNoPlaceholder": "Please enter inspection number", "inspectionStatus": "Inspection Status", "licensePlateNo": "License Plate", "licensePlateNoPlaceholder": "Please enter license plate", "repairmanName": "Repairman Name", "repairmanNamePlaceholder": "Please enter repairman name", "technician": "Technician", "technicianPlaceholder": "Please select technician", "repairmanPhone": "Repairman Phone", "repairmanPhonePlaceholder": "Please enter repairman phone", "createTime": "Create Time"}, "status": {"pending": "Pending", "in_progress": "In Progress", "pending_confirm": "Pending Confirm", "confirmed": "Confirmed"}, "table": {"inspectionNo": "Inspection No", "inspectionStatus": "Inspection Status", "repairmanName": "Repairman Name", "repairmanPhone": "Repairman Phone", "licensePlateNo": "License Plate", "vehicleModel": "Vehicle Model", "vehicleConfig": "Vehicle Config", "color": "Color", "mileage": "Mileage", "vehicleAge": "Vehicle Age", "serviceAdvisor": "Service Advisor", "technician": "Technician", "registerType": "Register Type", "serviceType": "Service Type", "customerConfirmTime": "Customer Confirm Time", "createTime": "Create Time", "updateTime": "Update Time"}, "actions": {"assign": "Assign Technician", "submitForConfirm": "Submit for Confirm", "recall": "Recall", "customerConfirm": "Customer Confirm", "viewDetail": "View Detail", "editDetail": "<PERSON> Detail"}, "dialog": {"assignTitle": "Assign Technician", "confirmTitle": "Customer Confirm", "detailTitle": "Inspection Detail", "technicianLabel": "Select Technician", "confirmTimeLabel": "Confirm Time", "remarksLabel": "Remarks", "remarksPlaceholder": "Please enter remarks"}, "registerType": {"appointment": "Appointment", "walk_in": "Walk-in"}, "serviceType": {"maintenance": "Maintenance", "repair": "Repair"}, "messages": {"assignSuccess": "Technician assigned successfully", "submitSuccess": "Submitted successfully", "recallSuccess": "Recalled successfully", "confirmSuccess": "Confirmed successfully", "selectTechnician": "Please select technician", "confirmAssign": "Are you sure to assign technician?", "confirmSubmit": "Are you sure to submit for customer confirm?", "confirmRecall": "Are you sure to recall?", "confirmCustomerConfirm": "Are you sure to confirm?"}}, "workOrder": {"title": "Work Order Management", "searchForm": {"workOrderNumber": "Work Order No", "workOrderNumberPlaceholder": "Please enter work order number", "status": "Status", "priority": "Priority", "customerSource": "Customer Source", "workOrderType": "Work Order Type", "customerName": "Customer Name", "customerNamePlaceholder": "Please enter customer name", "customerPhone": "Customer Phone", "customerPhonePlaceholder": "Please enter customer phone", "licensePlate": "License Plate", "licensePlatePlaceholder": "Please enter license plate", "serviceAdvisor": "Service Advisor", "serviceAdvisorPlaceholder": "Please select service advisor", "technician": "Technician", "technicianPlaceholder": "Please select technician", "createdAt": "Created At"}, "status": {"draft": "Draft", "pending_confirmation": "Pending Confirmation", "confirmed": "Confirmed", "in_progress": "In Progress", "completed": "Completed", "cancelled": "Cancelled", "rejected": "Rejected"}, "priority": {"normal": "Normal", "urgent": "<PERSON><PERSON>"}, "customerSource": {"appointment": "Appointment", "walk_in": "Walk-in", "undefined": "Undefined"}, "workOrderType": {"repair": "Repair", "maintenance": "Maintenance", "claim": "<PERSON><PERSON><PERSON>", "undefined": "Undefined"}, "paymentStatus": {"unpaid": "Unpaid", "partial": "Partial", "paid": "Paid"}, "table": {"workOrderNumber": "Work Order No", "status": "Status", "priority": "Priority", "customerSource": "Customer Source", "workOrderType": "Work Order Type", "customerName": "Customer Name", "customerPhone": "Customer Phone", "licensePlate": "License Plate", "vehicleModel": "Vehicle Model", "serviceAdvisor": "Service Advisor", "technician": "Technician", "estimatedAmount": "Estimated Amount", "actualAmount": "Actual Amount", "paymentStatus": "Payment Status", "createdAt": "Created At", "completedAt": "Completed At"}, "actions": {"create": "Create Work Order", "edit": "Edit", "view": "View Detail", "delete": "Delete", "cancel": "Cancel Work Order", "confirm": "Confirm Work Order", "complete": "Complete Work Order", "print": "Print", "export": "Export", "addItems": "Add Items", "submitApproval": "Submit Approval"}, "dialog": {"createTitle": "Create Work Order", "editTitle": "Edit Work Order", "detailTitle": "Work Order Detail", "statusChangeTitle": "Status Change", "deleteConfirm": "Are you sure to delete this work order?", "cancelConfirm": "Are you sure to cancel this work order?", "completeConfirm": "Are you sure to complete this work order?", "statusChangeReason": "Change Reason", "statusChangeReasonPlaceholder": "Please enter status change reason"}, "form": {"basicInfo": "Basic Information", "customerInfo": "Customer Information", "vehicleInfo": "Vehicle Information", "serviceInfo": "Service Information", "serviceItems": "Service Items", "partItems": "Part Items", "laborItems": "Labor Items", "notes": "Notes", "notesPlaceholder": "Please enter notes"}, "messages": {"createSuccess": "Work order created successfully", "updateSuccess": "Work order updated successfully", "deleteSuccess": "Work order deleted successfully", "statusChangeSuccess": "Status changed successfully", "operationFailed": "Operation failed", "confirmDelete": "Are you sure to delete this work order? This operation cannot be undone.", "confirmCancel": "Are you sure to cancel this work order?", "confirmComplete": "Are you sure to complete this work order?"}}, "workAssignment": {"title": "Work Assignment Management", "searchForm": {"workOrderId": "Work Order ID", "workOrderIdPlaceholder": "Please enter work order ID", "workOrderNo": "Work Order No", "workOrderNoPlaceholder": "Please enter work order number", "customerName": "Customer Name", "customerNamePlaceholder": "Please enter customer name", "licensePlate": "License Plate", "licensePlatePlaceholder": "Please enter license plate", "status": "Status", "workOrderType": "Work Order Type", "priority": "Priority", "serviceAdvisor": "Service Advisor", "assignedTechnician": "Assigned Technician", "creationTime": "Creation Time"}, "status": {"pending_assign": "Pending Assignment", "pending_start": "Pending Start", "in_progress": "In Progress", "completed": "Completed", "cancelled": "Cancelled", "on_hold": "On Hold"}, "workOrderType": {"maintenance": "Maintenance", "repair": "Repair", "inspection": "Inspection", "insurance": "Insurance"}, "priority": {"low": "Low", "normal": "Normal", "high": "High", "urgent": "<PERSON><PERSON>"}, "technicianStatus": {"available": "Available", "busy": "Busy", "offline": "Offline"}, "table": {"workOrderNo": "Work Order No", "customerName": "Customer Name", "vehicleInfo": "Vehicle Info", "status": "Status", "workOrderType": "Type", "priority": "Priority", "serviceAdvisor": "Service Advisor", "assignedTechnician": "Assigned Technician", "estimatedDuration": "Estimated Duration", "scheduledStartTime": "Scheduled Start Time", "creationTime": "Creation Time", "currentTechnician": "Current Technician"}, "actions": {"assign": "Assign", "reassign": "Reassign", "detail": "Detail", "cancel": "Cancel", "confirm": "Confirm", "search": "Search", "reset": "Reset", "export": "Export"}, "dialog": {"assignTitle": "Assign Work Order", "reassignTitle": "Reassign Work Order", "selectTechnician": "Select Technician", "estimatedStartTime": "Estimated Start Time", "estimatedDuration": "Estimated Duration", "notes": "Notes", "notesPlaceholder": "Please enter notes", "reason": "Reassignment Reason", "reasonPlaceholder": "Please enter reassignment reason"}, "technician": {"name": "Technician Name", "status": "Status", "skillLevel": "Skill Level", "specialties": "Specialties", "currentWorkload": "Current Workload", "workingHours": "Working Hours", "contactInfo": "Contact Info", "currentTechnician": "Current Technician", "selectTechnician": "Please select technician"}, "messages": {"assignSuccess": "Work order assigned successfully", "reassignSuccess": "Work order reassigned successfully", "operationFailed": "Operation failed", "loadDataFailed": "Failed to load data", "selectTechnicianFirst": "Please select technician first", "confirmAssign": "Are you sure to assign this work order?", "confirmReassign": "Are you sure to reassign this work order?"}, "common": {"unassigned": "Unassigned", "notScheduled": "Not Scheduled", "noData": "No Data", "loading": "Loading...", "total": "Total", "items": "items", "cancel": "Cancel", "confirm": "Confirm", "actions": "Actions", "detail": "Detail", "warning": "Warning", "success": "Success", "error": "Error", "info": "Info"}}, "workAssignmentDashboard": {"title": "Work Assignment Dashboard", "statistics": {"totalOrders": "Total Orders", "pendingAssignment": "Pending Assignment", "assignedOrders": "Assigned Orders", "inProgressOrders": "In Progress Orders", "completedOrders": "Completed Orders", "averageAssignmentTime": "Average Assignment Time", "technicianUtilization": "Technician Utilization", "todayCompleted": "Today Completed", "todayNew": "Today New"}, "charts": {"workloadTitle": "Technician Workload", "statusDistributionTitle": "Order Status Distribution", "orderFlowTitle": "Order Flow Trend", "performanceTitle": "Technician Performance", "departmentWorkloadTitle": "Department Workload"}, "schedule": {"title": "Technician Schedule", "technicianName": "Technician Name", "department": "Department", "workingHours": "Working Hours", "currentStatus": "Current Status", "assignedOrders": "Assigned Orders", "workloadPercentage": "Workload Percentage", "efficiency": "Efficiency", "availableCapacity": "Available Capacity", "specialties": "Specialties"}, "status": {"available": "Available", "busy": "Busy", "break": "Break", "offline": "Offline", "scheduled": "Scheduled", "in_progress": "In Progress", "completed": "Completed", "delayed": "Delayed"}, "workOrderType": {"maintenance": "Maintenance", "repair": "Repair", "inspection": "Inspection", "insurance": "Insurance"}, "priority": {"low": "Low", "normal": "Normal", "high": "High", "urgent": "<PERSON><PERSON>"}, "filters": {"date": "Date", "department": "Department", "technician": "Technician", "workOrderType": "Work Order Type", "priority": "Priority", "all": "All"}, "realTime": {"title": "Real-time Updates", "enabled": "Enable Real-time Updates", "interval": "Update Interval", "lastUpdate": "Last Update", "autoRefresh": "Auto Refresh", "refreshNow": "Refresh Now"}, "settings": {"title": "Dashboard Settings", "layout": "Layout", "theme": "Theme", "animation": "Animation", "responsive": "Responsive", "displayOptions": "Display Options", "showStatistics": "Show Statistics", "showCharts": "Show Charts", "showSchedule": "Show Schedule", "showRealTimeUpdates": "Show Real-time Updates"}, "performance": {"completedToday": "Completed Today", "completedWeek": "Completed This Week", "completedMonth": "Completed This Month", "averageTime": "Average Time", "qualityScore": "Quality Score", "satisfaction": "Customer Satisfaction", "onTimeRate": "On-time Completion Rate"}, "common": {"minutes": "minutes", "hours": "hours", "percentage": "%", "orders": "orders", "loading": "Loading...", "noData": "No Data", "refresh": "Refresh", "export": "Export", "fullscreen": "Fullscreen", "settings": "Settings"}}, "workOrderApproval": {"title": "Work Order Approval", "pendingApproval": "Pending Approval", "completedApproval": "Completed Approval", "approvalNo": "Approval No.", "approvalType": "Approval Type", "submitter": "Submitter", "submitTime": "Submit Time", "orderNo": "Order No.", "requestReason": "Request Reason", "customerName": "Customer Name", "customerInfo": "Customer Info", "licensePlate": "License Plate", "vehicleInfo": "Vehicle Info", "vehicleModel": "Vehicle Model", "storeName": "Store Name", "timeoutStatus": "Timeout Status", "currentLevel": "Current Level", "approvalResult": "A<PERSON><PERSON><PERSON> Result", "approvalTime": "Approval Time", "approverName": "Approver", "approvalStatus": "Approval Status", "claimApproval": "<PERSON><PERSON><PERSON>", "cancelApproval": "<PERSON>cel <PERSON>", "timeoutStatusSelect": {"normal": "Normal", "aboutToTimeout": "About to Timeout", "timeout": "Timeout"}, "level": {"firstLevel": "First Level", "secondLevel": "Second Level"}, "result": {"approved": "Approved", "rejected": "Rejected"}, "status": {"pendingReview": "Pending Review", "reviewed": "Reviewed"}, "searchForm": {"approvalNoPlaceholder": "Enter approval number", "submitterPlaceholder": "Enter submitter name", "orderNoPlaceholder": "Enter order number", "customerNamePlaceholder": "Enter customer name", "licensePlatePlaceholder": "Enter license plate"}, "actions": {"title": "Actions", "approve": "Approve", "detail": "Detail"}, "messages": {"loadListFailed": "Failed to load list", "loadDetailFailed": "Failed to load detail", "approvalSuccess": "Approval successful", "approvalFailed": "Approval failed", "exportSuccess": "Export successful", "exportFailed": "Export failed", "selectApprovalResult": "Please select approval result", "enterRejectionReason": "Please enter rejection reason"}, "dialog": {"claimApprovalTitle": "<PERSON><PERSON><PERSON>", "cancelApprovalTitle": "<PERSON>cel <PERSON>", "approvalDetail": "Approval Detail", "basicInfo": "Basic Information", "customerVehicleInfo": "Customer & Vehicle Info", "claimDetails": "<PERSON><PERSON><PERSON>", "laborClaim": "Labor Claim", "partsClaim": "Parts Claim", "laborCode": "Labor Code", "laborName": "Labor Name", "laborHours": "Labor Hours", "laborRate": "Labor Rate", "laborAmount": "Labor Amount", "laborTotal": "Labor Total", "partCode": "Part Code", "partName": "Part Name", "quantity": "Quantity", "unitPrice": "Unit Price", "totalAmount": "Total Amount", "partsTotal": "Parts Total", "approvalForm": "Approval Form", "approvalResult": "A<PERSON><PERSON><PERSON> Result", "approvalRemark": "Approval Remark", "approvalRemarkPlaceholder": "Enter approval remark (optional)", "rejectionReason": "Rejection Reason", "rejectionReasonPlaceholder": "Enter rejection reason", "cancelReason": "Cancel Reason", "cancelDescription": "Cancel Description", "requestReason": "Request Reason", "vehicleDetails": "Vehicle Details", "vin": "VIN", "engineNo": "Engine No.", "purchaseDate": "Purchase Date", "warrantyEndDate": "Warranty End Date", "mileage": "Mileage", "customerPhone": "Customer Phone", "licensePlate": "License Plate", "vehicleModel": "Vehicle Model", "approvalProcess": "Approval Process", "approver": "Approver"}}, "dispatch": {"title": "Workshop Dispatch", "subtitle": "Work Order Assignment & Progress Management", "workOrderNo": "Work Order No.", "priorityLabel": "Priority", "workOrderTypeLabel": "Work Order Type", "workOrderStatusLabel": "Work Order Status", "assignmentStatusLabel": "Assignment Status", "creationTime": "Creation Time", "customerSourceLabel": "Customer Source", "repairmanName": "Customer Name", "repairmanPhone": "Contact Phone", "licensePlateNumber": "License Plate", "vehicleModel": "Vehicle Model", "configuration": "Configuration", "color": "Color", "vehicleAge": "Vehicle Age (months)", "mileage": "Mileage (km)", "serviceAdvisor": "Service Advisor", "technicianLabel": "Technician", "estimatedWorkHours": "Estimated Work Hours", "estimatedStartTime": "Estimated Start Time", "estimatedFinishTime": "Estimated Finish Time", "actualStartTime": "Actual Start Time", "actualFinishTime": "Actual Finish Time", "completionTime": "Completion Time", "qualityInspector": "Quality Inspector", "qualityInspectionTime": "Quality Inspection Time", "isPaused": "IS Paused", "workLog": "Work Log", "notes": "Notes", "pauseReason": "Pause Reason", "workingHours": "Working Hours", "basicInfo": "Basic Info", "customerInfo": "Customer Info", "vehicleInfo": "Vehicle Info", "serviceInfo": "Service Info", "assignmentInfo": "Assign <PERSON><PERSON>", "progressInfo": "Process Info", "serviceDescription": "Service Description", "symptoms": "Symptoms", "diagnosis": "Diagnosis", "priority": {"urgent": "<PERSON><PERSON>", "normal": "Normal"}, "workOrderType": {"repair": "Repair", "maintenance": "Maintenance", "insurance": "Insurance"}, "workOrderStatus": {"pendingAssignment": "Pending Assignment", "pendingStart": "Pending Start", "inProgress": "In Progress", "paused": "Paused", "completed": "Completed", "cancelled": "Cancelled", "pendingQualityInspection": "Pending Quality Inspection"}, "assignmentStatus": {"pending": "Pending", "assigned": "Assigned"}, "customerSource": {"appointment": "Appointment", "walkIn": "Walk-in"}, "technicianStatus": {"available": "Available", "busy": "Busy", "offline": "Offline"}, "searchForm": {"title": "Filter Conditions", "workOrderNoPlaceholder": "Enter work order number", "repairmanNamePlaceholder": "Enter customer name", "licensePlateNumberPlaceholder": "Enter license plate", "serviceAdvisorPlaceholder": "Enter service advisor", "technicianPlaceholder": "Enter technician name"}, "actions": {"assign": "Assign", "reassign": "Reassign", "detail": "Detail", "pause": "Pause", "resume": "Resume", "complete": "Complete", "batchAssign": "<PERSON><PERSON>", "export": "Export", "refresh": "Refresh"}, "dialog": {"assignTitle": "Assign Work Order", "reassignTitle": "Reassign Work Order", "detailTitle": "Work Order Detail", "selectTechnician": "Select Technician", "selectNewTechnician": "Select New Technician", "originalTechnician": "Original Technician", "estimatedStartTime": "Estimated Start Time", "estimatedFinishTime": "Estimated Finish Time", "estimatedWorkHours": "Estimated Work Hours", "notes": "Notes", "reason": "Reassignment Reason", "pauseReason": "Pause Reason", "confirmAssign": "Confirm Assignment", "confirmReassign": "Confirm Reassignment", "confirmPause": "Confirm Pause", "confirmResume": "Confirm Resume", "confirmComplete": "Confirm Complete"}, "table": {"operations": "Operations", "noData": "No Data", "loading": "Loading..."}, "messages": {"assignSuccess": "Assignment successful", "assignFailed": "Assignment failed", "reassignSuccess": "Reassignment successful", "reassignFailed": "Reassignment failed", "pauseSuccess": "Pause successful", "pauseFailed": "Pause failed", "resumeSuccess": "Resume successful", "resumeFailed": "Resume failed", "completeSuccess": "Complete successful", "completeFailed": "Complete failed", "exportSuccess": "Export successful", "exportFailed": "Export failed", "loadListFailed": "Failed to load list", "loadDetailFailed": "Failed to load detail", "selectWorkOrder": "Please select work order", "selectTechnician": "Please select technician", "inputStartTime": "Please input start time", "inputFinishTime": "Please input finish time", "inputWorkHours": "Please input work hours", "inputReason": "Please input reason", "confirmOperation": "Confirm this operation?"}, "statistics": {"totalWorkOrders": "Total Work Orders", "pendingAssignment": "Pending Assignment", "inProgress": "In Progress", "completed": "Completed", "paused": "Paused", "averageCompletionTime": "Average Completion Time (hours)", "technicianUtilization": "Technician Utilization"}, "technician": {"status": "Status", "workload": "Workload", "currentWorkOrders": "Current Work Orders", "maxCapacity": "Max Capacity", "utilizationRate": "Utilization Rate", "estimatedAvailableTime": "Estimated Available Time", "skills": "Skills", "department": "Department", "experience": "Experience (years)", "efficiency": "Efficiency"}}, "qualityCheck": {"title": "Quality Management", "subtitle": "Quality Check Management & Quality Control", "qualityCheckNo": "Quality Check No.", "workOrderNo": "Work Order No.", "statusLabel": "Quality Check Status", "workOrderTypeLabel": "Work Order Type", "technicianName": "Technician", "plateNumber": "License Plate", "serviceCustomerName": "Customer Name", "serviceCustomerPhone": "Customer Phone", "vehicleModel": "Vehicle Model", "vehicleConfig": "Configuration", "vehicleColor": "Color", "createTime": "Create Time", "startTime": "Start Time", "finishTime": "Finish Time", "estimatedHours": "Estimated Hours", "actualHours": "Actual Hours", "isClaimRelated": "<PERSON><PERSON><PERSON>lated", "isOutsourceRelated": "Outsource Related", "reworkReason": "Rework Reason", "reworkRequirement": "Rework Requirement", "auditRemark": "Audit Remark", "vin": "VIN", "vehicleAge": "Vehicle Age (months)", "mileage": "Mileage (km)", "serviceDate": "Service Date", "serviceLocation": "Service Location", "serviceNotes": "Service Notes", "qualityInspector": "Quality Inspector", "qualityInspectionTime": "Quality Inspection Time", "checkItems": "Check Items", "workOrderInfo": "Work Order Info", "vehicleInfo": "Vehicle Info", "serviceInfo": "Service Info", "history": "Operation History", "basicInfo": "Basic Info", "serviceAdvisor": "Service Advisor", "serviceType": "Service Type", "serviceDescription": "Service Description", "symptoms": "Symptoms", "diagnosis": "Diagnosis", "checkResultLabel": "Check Result", "standardValue": "Standard Value", "actualValue": "Actual Value", "unit": "Unit", "required": "Required", "optional": "Optional", "category": "Category", "itemName": "Item Name", "status": {"pending_check": "Pending Check", "checking": "Checking", "pending_review": "Pending Review", "passed": "Passed", "rework": "Rework"}, "workOrderType": {"maintenance": "Maintenance", "repair": "Repair", "insurance": "Insurance"}, "itemType": {"BOOLEAN": "Boolean", "NUMERIC": "Numeric", "TEXT": "Text"}, "checkResult": {"PASS": "Pass", "FAIL": "Fail"}, "auditResult": {"passed": "<PERSON><PERSON> Passed", "rework": "Rework"}, "searchForm": {"title": "Filter Conditions", "qualityCheckNoPlaceholder": "Enter quality check number", "workOrderNoPlaceholder": "Enter work order number", "technicianNamePlaceholder": "Enter technician name", "plateNumberPlaceholder": "Enter license plate", "serviceCustomerNamePlaceholder": "Enter customer name"}, "actions": {"start": "Start Check", "submit": "Submit Check", "audit": "Audit", "rework": "Rework", "detail": "Detail", "edit": "Edit", "export": "Export", "batchAudit": "<PERSON><PERSON>", "saveDraft": "Save Draft", "pause": "Pause", "resume": "Resume", "cancel": "Cancel"}, "dialog": {"submitTitle": "Submit Quality Check Result", "auditTitle": "Quality Check Audit", "detailTitle": "Quality Check Detail", "editTitle": "Edit Quality Check", "reworkTitle": "Rework Process", "confirmSubmit": "Confirm Submit", "confirmAudit": "Confirm Audit", "confirmRework": "Confirm Rework", "selectAuditResult": "Please select audit result", "inputReworkReason": "Please input rework reason", "inputReworkRequirement": "Please input rework requirement", "inputAuditRemark": "Please input audit remark"}, "table": {"operations": "Operations", "noData": "No Data", "loading": "Loading..."}, "messages": {"startSuccess": "Quality check started", "startFailed": "Failed to start quality check", "submitSuccess": "Quality check result submitted successfully", "submitFailed": "Failed to submit quality check result", "auditSuccess": "Quality check audit completed", "auditFailed": "Quality check audit failed", "reworkSuccess": "Rework process completed", "reworkFailed": "Rework process failed", "exportSuccess": "Data export successful", "exportFailed": "Data export failed", "saveDraftSuccess": "Draft saved successfully", "saveDraftFailed": "Failed to save draft", "loadListFailed": "Failed to load list", "loadDetailFailed": "Failed to load detail", "selectQualityCheck": "Please select quality check", "selectAuditResult": "Please select audit result", "inputReworkReason": "Please input rework reason", "confirmOperation": "Confirm this operation?", "batchOperationSuccess": "Batch operation successful", "batchOperationFailed": "Batch operation failed"}, "statistics": {"todayTotal": "Today Total", "todayPassed": "Today Passed", "todayFailed": "Today Failed", "weekTotal": "Week Total", "weekPassed": "Week Passed", "weekFailed": "Week Failed", "passRate": "Pass Rate", "avgProcessTime": "Average Process Time (hours)", "reworkRate": "Rework Rate", "monthlyTrend": "Monthly Trend"}, "checkItem": {"category": "Check Category", "itemName": "Check <PERSON>em", "standardValue": "Standard Value", "actualValue": "Actual Value", "checkResult": "Check Result", "unit": "Unit", "required": "Required", "optional": "Optional"}, "tabs": {"basicInfo": "Basic Info", "checkItems": "Check Items", "workOrderInfo": "Work Order Info", "vehicleInfo": "Vehicle Info", "serviceInfo": "Service Info", "history": "Operation History"}, "reworkReasons": {"quality_fail": "Quality Check Failed", "process_fail": "Process Standard Not Met", "parts_issue": "Parts Quality Issue", "operation_issue": "Operation Issue", "customer_request": "Customer Request Rework", "other": "Other Reasons"}, "showDetails": "Show Details", "hideDetails": "Hide Details"}, "settlement": {"title": "Settlement Management", "subtitle": "Settlement Management & Payment Processing", "settlementNo": "Settlement No.", "workOrderNo": "Work Order No.", "settlementStatus": "Settlement Status", "paymentStatusLabel": "Payment Status", "customerName": "Customer Name", "customerPhone": "Customer Phone", "vehiclePlate": "License Plate", "vehicleModel": "Vehicle Model", "vehicleConfig": "Configuration", "vehicleColor": "Color", "technician": "Technician", "serviceAdvisor": "Service Advisor", "totalAmount": "Total Amount", "paidAmount": "<PERSON><PERSON>", "payableAmount": "Payable Amount", "createdAt": "Created Time", "inspectionStatus": "Inspection Status", "vin": "VIN", "vehicleAge": "Vehicle Age", "mileage": "Mileage", "servicePackage": "Service Package", "laborItems": "Labor Items", "partItems": "Part Items", "operationLogs": "Operation Logs", "operationType": "Operation Type", "operator": "Operator", "operationTime": "Operation Time", "operationContent": "Operation Content", "remarks": "Remarks", "status": {"pre_settlement": "Pre Settlement", "pending_settlement": "Pending Settlement", "completed": "Completed", "cancelled": "Cancelled"}, "paymentStatus": {"pending": "Pending", "deposit_paid": "<PERSON><PERSON><PERSON><PERSON>", "fully_paid": "<PERSON>y Paid", "refunding": "Refunding", "refunded": "Refunded"}, "workOrderType": {"maintenance": "Maintenance", "repair": "Repair", "warranty": "Warranty"}, "paymentMethod": {"cash": "Cash", "pos": "POS", "wechat": "WeChat Pay", "alipay": "Alipay", "bank_transfer": "Bank Transfer", "undefined": "Undefined"}, "searchForm": {"title": "Filter Conditions", "settlementNoPlaceholder": "Enter settlement number", "workOrderNoPlaceholder": "Enter work order number", "customerNamePlaceholder": "Enter customer name", "vehiclePlatePlaceholder": "Enter license plate", "technicianPlaceholder": "Enter technician name", "serviceAdvisorPlaceholder": "Enter service advisor", "amountRange": "Amount Range", "amountMinPlaceholder": "Min amount", "amountMaxPlaceholder": "Max amount"}, "actions": {"push": "Push Settlement", "payment": "Payment", "detail": "Detail", "complete": "Complete Settlement", "cancel": "Cancel Settlement", "export": "Export", "batchPush": "<PERSON><PERSON>", "refresh": "Refresh"}, "dialog": {"detailTitle": "Settlement Detail", "paymentTitle": "Payment Management", "confirmPush": "Confirm <PERSON><PERSON>", "confirmComplete": "Confirm Complete", "confirmCancel": "Confirm Cancel", "selectPaymentMethod": "Please select payment method", "inputAmount": "Please input amount", "inputRemarks": "Please input remarks"}, "table": {"operations": "Operations", "noData": "No Data", "loading": "Loading..."}, "messages": {"pushSuccess": "Settlement pushed successfully", "pushFailed": "Failed to push settlement", "paymentSuccess": "Payment processed successfully", "paymentFailed": "Payment processing failed", "completeSuccess": "Settlement completed", "completeFailed": "Failed to complete settlement", "cancelSuccess": "Settlement cancelled successfully", "cancelFailed": "Failed to cancel settlement", "exportSuccess": "Data export successful", "exportFailed": "Data export failed", "loadListFailed": "Failed to load list", "loadDetailFailed": "Failed to load detail", "selectSettlement": "Please select settlement", "confirmOperation": "Confirm this operation?", "batchOperationSuccess": "Batch operation successful", "batchOperationFailed": "Batch operation failed"}, "statistics": {"todayTotal": "Today Total", "todayCompleted": "Today Completed", "todayAmount": "Today Amount", "weekTotal": "Week Total", "weekCompleted": "Week Completed", "weekAmount": "Week Amount", "monthTotal": "Month Total", "monthCompleted": "Month Completed", "monthAmount": "Month Amount", "completionRate": "Completion Rate", "avgSettlementAmount": "Average Settlement Amount", "monthlyTrend": "Monthly Trend", "completionTrend": "Completion Trend"}, "laborItem": {"laborCode": "Labor Code", "laborName": "Labor Name", "standardHours": "Standard Hours", "actualHours": "Actual Hours", "unitPrice": "Unit Price", "totalAmount": "Total Amount", "receivableAmount": "Receivable Amount", "warrantyAmount": "<PERSON><PERSON><PERSON> Amount", "discountAmount": "Discount Amount", "remarks": "Remarks"}, "partItem": {"partCode": "Part Code", "partName": "Part Name", "specification": "Specification", "unit": "Unit", "quantity": "Quantity", "unitPrice": "Unit Price", "totalAmount": "Total Amount", "receivableAmount": "Receivable Amount", "warrantyAmount": "<PERSON><PERSON><PERSON> Amount", "discountAmount": "Discount Amount", "remarks": "Remarks"}, "paymentRecord": {"paymentNo": "Payment No.", "businessType": "Business Type", "transactionNo": "Transaction No.", "paymentMethod": "Payment Method", "amount": "Amount", "paymentType": "Payment Type", "paymentTime": "Payment Time", "remarks": "Remarks"}, "tabs": {"basicInfo": "Basic Info", "customerInfo": "Customer Info", "vehicleInfo": "Vehicle Info", "serviceInfo": "Service Info", "laborItems": "Labor Items", "partItems": "Part Items", "paymentRecords": "Payment Records", "operationLogs": "Operation Logs"}}}