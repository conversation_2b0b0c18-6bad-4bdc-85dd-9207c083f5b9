<!--
  审批详情对话框组件
  已完成国际化改造，使用 useModuleI18n('orderApproval') 和 useModuleI18n('common')
  支持中英文切换
  数据从列表中获取，不从接口获取
-->
<template>
  <el-dialog
    v-model="currentVisible"
    :title="t('approvalDetail')"
    width="700px"
    :before-close="handleClose"
    class="approval-detail-dialog"
  >
    <div class="dialog-content">
      <h3 class="section-title">{{ t('approvalInfo') }}</h3>

      <!-- 无数据提示 -->
      <div v-if="!approvalData" class="no-data-info">
        {{ tc('noData') }}
      </div>

      <div v-if="approvalData" class="detail-card">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">{{ t('approvalNumber') }}:</span>
              <span class="detail-value">{{ approvalData.approvalNo  || 'N/A' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">{{ t('approvalType') }}:</span>
              <span class="detail-value">{{ getApprovalTypeText(approvalData.approvalType) }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">{{ t('submittedBy') }}:</span>
              <span class="detail-value">{{ approvalData.submitterName || 'N/A' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">{{ t('submissionTime') }}:</span>
              <span class="detail-value">{{ approvalData.submitTime || 'N/A' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">{{ t('orderNumber') }}:</span>
              <span class="detail-value">{{ approvalData.orderNo || 'N/A' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">{{ t('applicationReason') }}:</span>
              <span class="detail-value">{{ approvalData.reason || 'N/A' }}</span>
            </div>
          </el-col>
           <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">{{ t('approvalResult') }}:</span>
              <span class="detail-value">
                <el-tag v-if="approvalData.approvalResult" :type="getApprovalResultTagType(approvalData.approvalResult)">
                  {{ getApprovalResultText(approvalData.approvalResult) }}
                </el-tag>
                <span v-else>N/A</span>
              </span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">{{ t('approvalTime') }}:</span>
              <span class="detail-value">{{ approvalData.approvalTime || 'N/A' }}</span>
            </div>
          </el-col>
          <el-col :span="24">
            <div class="detail-item">
              <span class="detail-label">{{ t('approvalComment') }}:</span>
              <span class="detail-value">{{ approvalData.comment  || 'N/A' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
       <div v-else class="empty-state">
        <p>{{ tc('noData') }}</p>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">{{ tc('close') }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { computed, watch } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { OrderApprovalListItem, ApprovalType, ApprovalResult } from '@/types/sales/orderApproval';

const props = defineProps<{
  visible: boolean;
  approvalData: OrderApprovalListItem | null;
}>();

const emit = defineEmits(['update:visible', 'close']);

const { t } = useModuleI18n('sales.orderApproval');
const { tc } = useModuleI18n('common');

const currentVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val),
});

// 添加调试信息
watch(() => props.approvalData, (newData) => {
  console.log('ApprovalDetailDialog received data:', newData);
}, { immediate: true });

const handleClose = () => {
  currentVisible.value = false;
  emit('close');
};

// Helper functions for display
const getApprovalTypeText = (type: ApprovalType) => {
  const typeTextMap: Record<ApprovalType, string> = {
    cancel_order: t('cancelOrderApproval'),
    modify_info: t('modifyOrderApproval'),
  };
  return typeTextMap[type] || type;
};

const getApprovalResultTagType = (result: ApprovalResult) => {
  const resultMap: Record<ApprovalResult, 'success' | 'danger'> = {
    approved: 'success',
    rejected: 'danger',
  };
  return resultMap[result] || 'info';
};

const getApprovalResultText = (result: ApprovalResult) => {
  const resultTextMap: Record<ApprovalResult, string> = {
    approved: t('approved'),
    rejected: t('rejected'),
  };
  return resultTextMap[result] || result;
};
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 10px 20px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #333;
}

.detail-card {
  background-color: #f9f9f9;
  border: 1px solid #e3e3e3;
  border-radius: 4px;
  padding: 20px;
}

.detail-item {
  display: flex;
  margin-bottom: 18px;
  font-size: 14px;
  line-height: 1.5;

  &:last-child {
    margin-bottom: 0;
  }
}

.detail-label {
  width: 100px;
  color: #666;
  text-align: right;
  margin-right: 15px;
  flex-shrink: 0;
}

.detail-value {
  color: #333;
  word-break: break-all;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
  color: #999;
}
</style>
