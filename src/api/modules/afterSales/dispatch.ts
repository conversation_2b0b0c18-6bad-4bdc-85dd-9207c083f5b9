import request from '@/api';
import type {
  DispatchListItem,
  DispatchSearchParams,
  TechnicianInfo,
  AssignmentFormData,
  ReassignmentFormData,
  WorkOrderDetail,
  PaginationResponse,
  ApiResponse,
  DispatchStatistics,
  TechnicianWorkload,
  BatchOperationData,
  ExportDispatchParams
} from '@/types/afterSales/dispatch';
import {
  getMockDispatchList,
  getMockTechnicianList,
  getMockWorkOrderDetail,
  submitMockAssignment,
  submitMockReassignment,
  getMockDispatchStatistics,
  getMockTechnicianWorkload,
  submitMockBatchOperation,
  exportMockDispatchData
} from '@/mock/data/afterSales/dispatch';

// 临时使用Mock数据开关
const USE_MOCK_API_TEMP = true;

/**
 * 获取派工列表
 */
export const getDispatchList = (
  params: DispatchSearchParams
): Promise<PaginationResponse<DispatchListItem>> => {
  if (USE_MOCK_API_TEMP) {
    return getMockDispatchList(params);
  }
  return request.get<any, PaginationResponse<DispatchListItem>>(
    '/after-sales/dispatch/list',
    { params }
  );
};

/**
 * 获取技师列表
 */
export const getTechnicianList = (): Promise<TechnicianInfo[]> => {
  if (USE_MOCK_API_TEMP) {
    return getMockTechnicianList();
  }
  return request.get<any, TechnicianInfo[]>('/after-sales/technicians');
};

/**
 * 获取工单详情
 */
export const getWorkOrderDetail = (workOrderNo: string): Promise<WorkOrderDetail> => {
  if (USE_MOCK_API_TEMP) {
    return getMockWorkOrderDetail(workOrderNo);
  }
  return request.get<any, WorkOrderDetail>(`/after-sales/dispatch/detail/${workOrderNo}`);
};

/**
 * 分配工单
 */
export const assignWorkOrder = (data: AssignmentFormData): Promise<ApiResponse> => {
  if (USE_MOCK_API_TEMP) {
    return submitMockAssignment(data);
  }
  return request.post<any, ApiResponse>('/after-sales/dispatch/assign', data);
};

/**
 * 重新分配工单
 */
export const reassignWorkOrder = (data: ReassignmentFormData): Promise<ApiResponse> => {
  if (USE_MOCK_API_TEMP) {
    return submitMockReassignment(data);
  }
  return request.post<any, ApiResponse>('/after-sales/dispatch/reassign', data);
};

/**
 * 暂停工单
 */
export const pauseWorkOrder = (workOrderNo: string, reason: string): Promise<ApiResponse> => {
  if (USE_MOCK_API_TEMP) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: '工单暂停成功'
        });
      }, 500);
    });
  }
  return request.post<any, ApiResponse>(`/after-sales/dispatch/pause/${workOrderNo}`, { reason });
};

/**
 * 恢复工单
 */
export const resumeWorkOrder = (workOrderNo: string): Promise<ApiResponse> => {
  if (USE_MOCK_API_TEMP) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: '工单恢复成功'
        });
      }, 500);
    });
  }
  return request.post<any, ApiResponse>(`/after-sales/dispatch/resume/${workOrderNo}`);
};

/**
 * 完成工单
 */
export const completeWorkOrder = (workOrderNo: string, notes?: string): Promise<ApiResponse> => {
  if (USE_MOCK_API_TEMP) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: '工单完成成功'
        });
      }, 500);
    });
  }
  return request.post<any, ApiResponse>(`/after-sales/dispatch/complete/${workOrderNo}`, { notes });
};

/**
 * 获取派工统计
 */
export const getDispatchStatistics = (): Promise<DispatchStatistics> => {
  if (USE_MOCK_API_TEMP) {
    return getMockDispatchStatistics();
  }
  return request.get<any, DispatchStatistics>('/after-sales/dispatch/statistics');
};

/**
 * 获取技师工作负载
 */
export const getTechnicianWorkload = (): Promise<TechnicianWorkload[]> => {
  if (USE_MOCK_API_TEMP) {
    return getMockTechnicianWorkload();
  }
  return request.get<any, TechnicianWorkload[]>('/after-sales/technicians/workload');
};

/**
 * 批量操作
 */
export const batchOperation = (data: BatchOperationData): Promise<ApiResponse> => {
  if (USE_MOCK_API_TEMP) {
    return submitMockBatchOperation(data);
  }
  return request.post<any, ApiResponse>('/after-sales/dispatch/batch', data);
};

/**
 * 导出派工数据
 */
export const exportDispatchData = (params: ExportDispatchParams): Promise<Blob> => {
  if (USE_MOCK_API_TEMP) {
    return exportMockDispatchData(params);
  }
  return request.get<any, Blob>(
    '/after-sales/dispatch/export',
    {
      params,
      responseType: 'blob'
    }
  );
};

/**
 * 获取可用技师列表（根据工单类型和时间）
 */
export const getAvailableTechnicians = (
  workOrderType: string,
  estimatedStartTime: string,
  estimatedWorkHours: number
): Promise<TechnicianInfo[]> => {
  if (USE_MOCK_API_TEMP) {
    return getMockTechnicianList().then(technicians =>
      technicians.filter(t => t.status === 'available')
    );
  }
  return request.get<any, TechnicianInfo[]>('/after-sales/technicians/available', {
    params: { workOrderType, estimatedStartTime, estimatedWorkHours }
  });
};

/**
 * 获取技师日程安排
 */
export const getTechnicianSchedule = (technicianId: string, date: string): Promise<any[]> => {
  if (USE_MOCK_API_TEMP) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve([
          {
            id: '1',
            workOrderNo: 'WO20241201001',
            startTime: '09:00',
            endTime: '11:00',
            status: 'inProgress'
          },
          {
            id: '2',
            workOrderNo: 'WO20241201002',
            startTime: '14:00',
            endTime: '16:30',
            status: 'pendingStart'
          }
        ]);
      }, 300);
    });
  }
  return request.get<any, any[]>(`/after-sales/technicians/${technicianId}/schedule`, {
    params: { date }
  });
};
