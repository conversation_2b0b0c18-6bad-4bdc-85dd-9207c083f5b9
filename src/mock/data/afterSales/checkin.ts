// src/mock/data/afterSales/checkin.ts

import type { 
  CheckinListParams, 
  CheckinPageResponse, 
  CheckinListItem,
  VehicleInfo,
  CheckinFormData 
} from '@/types/afterSales/checkin.d.ts';

// 生成动态 Mock 数据
function generateMockCheckinData(): CheckinListItem[] {
  const data: CheckinListItem[] = [];
  const licensePlates = ['京A12345', '沪B67890', '粤C11111', '浙D22222', '苏E33333', '川F44444', '鲁G55555'];
  const vehicleModels = ['奔驰C级', '宝马3系', '奥迪A4L', '凯迪拉克CT5', '沃尔沃S60'];
  const configurations = ['豪华版', '运动版', '舒适版', '尊贵版', '标准版'];
  const colors = ['珍珠白', '曜岩黑', '极地银', '天空蓝', '玛瑙红'];
  const serviceAdvisors = ['张三', '李四', '王五', '赵六', '钱七'];
  const repairPersonNames = ['陈先生', '刘女士', '杨先生', '周女士', '吴先生'];

  for (let i = 1; i <= 28; i++) {
    const createdDate = new Date();
    createdDate.setDate(createdDate.getDate() - Math.floor(Math.random() * 30));
    const updatedDate = new Date(createdDate.getTime() + Math.random() * 86400000);

    data.push({
      checkinId: `CK${String(i).padStart(6, '0')}`,
      licensePlate: licensePlates[Math.floor(Math.random() * licensePlates.length)],
      vin: `WBA${Math.random().toString(36).substr(2, 14).toUpperCase()}`,
      vehicleModel: vehicleModels[Math.floor(Math.random() * vehicleModels.length)],
      vehicleConfiguration: configurations[Math.floor(Math.random() * configurations.length)],
      color: colors[Math.floor(Math.random() * colors.length)],
      mileage: Math.floor(Math.random() * 100000) + 5000,
      vehicleAge: Math.floor(Math.random() * 60) + 6,
      repairPersonName: repairPersonNames[Math.floor(Math.random() * repairPersonNames.length)],
      repairPersonPhone: `138${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}`,
      serviceAdvisor: serviceAdvisors[Math.floor(Math.random() * serviceAdvisors.length)],
      relatedRepairOrderId: Math.random() > 0.6 ? `RO${String(i).padStart(6, '0')}` : null,
      serviceType: Math.random() > 0.5 ? '维修' : '保养',
      createdAt: createdDate.toISOString().slice(0, 16).replace('T', ' '),
      updatedAt: updatedDate.toISOString().slice(0, 16).replace('T', ' '),
      notes: Math.random() > 0.7 ? '客户反映发动机异响' : '',
      isDeleted: false
    });
  }
  
  return data.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
}

const mockCheckinData = generateMockCheckinData();

export const getCheckinList = (params: CheckinListParams): Promise<CheckinPageResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockCheckinData];

      // 应用筛选条件
      if (params.checkinId) {
        filteredData = filteredData.filter(item => 
          item.checkinId.toLowerCase().includes(params.checkinId!.toLowerCase())
        );
      }

      if (params.licensePlate) {
        filteredData = filteredData.filter(item => 
          item.licensePlate.toLowerCase().includes(params.licensePlate!.toLowerCase())
        );
      }

      if (params.repairPersonName) {
        filteredData = filteredData.filter(item => 
          item.repairPersonName.includes(params.repairPersonName!)
        );
      }

      if (params.repairPersonPhone) {
        filteredData = filteredData.filter(item => 
          item.repairPersonPhone.includes(params.repairPersonPhone!)
        );
      }

      if (params.createdAtStart && params.createdAtEnd) {
        filteredData = filteredData.filter(item => {
          const itemDate = item.createdAt.split(' ')[0];
          return itemDate >= params.createdAtStart! && itemDate <= params.createdAtEnd!;
        });
      }

      // 分页处理
      const page = params.page || 1;
      const pageSize = params.pageSize || 10;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        list: filteredData.slice(start, end),
        total: filteredData.length,
      });
    }, 300);
  });
};

export const queryVehicleInfo = (licensePlate: string): Promise<VehicleInfo | null> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟根据车牌号查询车辆信息
      const existingVehicle = mockCheckinData.find(item => item.licensePlate === licensePlate);
      
      if (existingVehicle) {
        resolve({
          licensePlate: existingVehicle.licensePlate,
          vin: existingVehicle.vin,
          vehicleModel: existingVehicle.vehicleModel,
          vehicleConfiguration: existingVehicle.vehicleConfiguration,
          color: existingVehicle.color,
          mileage: existingVehicle.mileage,
          vehicleAge: existingVehicle.vehicleAge
        });
      } else {
        resolve(null);
      }
    }, 500);
  });
};

export const addCheckinRecord = (data: CheckinFormData): Promise<{ success: boolean; checkinId: string }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const newId = `CK${String(mockCheckinData.length + 1).padStart(6, '0')}`;
      const now = new Date().toISOString().slice(0, 16).replace('T', ' ');

      const newRecord: CheckinListItem = {
        ...data,
        checkinId: newId,
        createdAt: now,
        updatedAt: now,
        isDeleted: false
      } as CheckinListItem;

      mockCheckinData.unshift(newRecord);

      resolve({
        success: true,
        checkinId: newId
      });
    }, 800);
  });
};

export const updateCheckinRecord = (checkinId: string, data: Partial<CheckinFormData>): Promise<{ success: boolean }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockCheckinData.findIndex(item => item.checkinId === checkinId);
      if (index !== -1) {
        mockCheckinData[index] = {
          ...mockCheckinData[index],
          ...data,
          updatedAt: new Date().toISOString().slice(0, 16).replace('T', ' ')
        };
      }

      resolve({ success: true });
    }, 800);
  });
};

export const deleteCheckinRecord = (checkinId: string): Promise<{ success: boolean }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockCheckinData.findIndex(item => item.checkinId === checkinId);
      if (index !== -1) {
        mockCheckinData.splice(index, 1);
      }

      resolve({ success: true });
    }, 500);
  });
};

export const createRelatedRepairOrder = (checkinId: string): Promise<{ success: boolean; repairOrderId: string }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockCheckinData.findIndex(item => item.checkinId === checkinId);
      if (index !== -1) {
        const repairOrderId = `RO${checkinId.slice(2)}`;
        mockCheckinData[index].relatedRepairOrderId = repairOrderId;
        mockCheckinData[index].updatedAt = new Date().toISOString().slice(0, 16).replace('T', ' ');
      }

      resolve({
        success: true,
        repairOrderId: `RO${checkinId.slice(2)}`
      });
    }, 1000);
  });
};
