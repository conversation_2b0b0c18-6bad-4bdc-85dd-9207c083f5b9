<template>
  <div class="checkin-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>{{ t('title') }}</h1>
    </div>

    <!-- 搜索表单 -->
    <CheckinSearchForm
      v-model:search-params="searchParams"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 操作区域 -->
    <el-card class="mb-20">
      <div class="action-bar">
        <div class="action-left">
          <!-- 预留位置 -->
        </div>
        <div class="action-right">
          <el-button type="primary" :icon="Plus" @click="handleAdd">
            {{ t('actions.add') }}
          </el-button>
          <el-button :icon="Download" @click="handleExport">
            {{ t('actions.export') }}
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <CheckinTable
      :checkin-list="checkinList"
      :loading="loading"
      :pagination="pagination"
      @view-details="handleViewDetails"
      @edit-record="handleEditRecord"
      @cancel-record="handleCancelRecord"
      @create-repair-order="handleCreateRepairOrder"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
    />

    <!-- 新增/编辑弹窗 -->
    <CheckinFormDialog
      v-model:visible="formDialogVisible"
      :is-edit="isEdit"
      :record-data="currentRecord"
      @submit="handleFormSubmit"
    />

    <!-- 详情弹窗 -->
    <CheckinDetailDialog
      v-model:visible="detailDialogVisible"
      :record-data="currentRecord"
    />

    <!-- 取消登记弹窗 -->
    <CancelCheckinDialog
      v-model:visible="cancelDialogVisible"
      :record-data="currentRecord"
      @confirm="handleCancelConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus';
import { Plus, Download } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import {
  getCheckinList,
  createCheckin,
  updateCheckin,
  cancelCheckin,
  createRepairOrder,
  exportCheckinList
} from '@/api/modules/afterSales/checkin';
import type {
  CheckinItem,
  CheckinSearchParams,
  CheckinFormData,
  CheckinPageResponse
} from '@/types/afterSales/checkin';

// 导入子组件
import CheckinSearchForm from './components/CheckinSearchForm.vue';
import CheckinTable from './components/CheckinTable.vue';
import CheckinFormDialog from './components/CheckinFormDialog.vue';
import CheckinDetailDialog from './components/CheckinDetailDialog.vue';
import CancelCheckinDialog from './components/CancelCheckinDialog.vue';

// 国际化
const { t } = useModuleI18n('afterSales.checkin');

// 响应式数据
const loading = ref(false);
const checkinList = ref<CheckinItem[]>([]);
const currentRecord = ref<CheckinItem | null>(null);

// 对话框状态
const formDialogVisible = ref(false);
const detailDialogVisible = ref(false);
const cancelDialogVisible = ref(false);
const isEdit = ref(false);

// 搜索参数
const searchParams = reactive<CheckinSearchParams>({
  checkinId: '',
  licensePlate: '',
  repairPersonName: '',
  repairPersonPhone: '',
  createdAtStart: '',
  createdAtEnd: '',
  status: '',
  pageNum: 1,
  pageSize: 10
});

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0,
  pages: 0
});

// 页面初始化
onMounted(() => {
  fetchCheckinList();
});

// 获取登记列表
const fetchCheckinList = async () => {
  loading.value = true;
  try {
    const params = {
      ...searchParams,
      pageNum: pagination.current,
      pageSize: pagination.size
    };

    const response: CheckinPageResponse = await getCheckinList(params);

    checkinList.value = response.result.records;
    pagination.total = response.result.total;
    pagination.pages = response.result.pages;

  } catch (error) {
    console.error('获取登记列表失败:', error);
    ElMessage.error('获取登记列表失败');
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchCheckinList();
};

// 重置
const handleReset = () => {
  pagination.current = 1;
  fetchCheckinList();
};

// 新增登记
const handleAdd = () => {
  currentRecord.value = null;
  isEdit.value = false;
  formDialogVisible.value = true;
};

// 编辑登记
const handleEditRecord = (record: CheckinItem) => {
  currentRecord.value = record;
  isEdit.value = true;
  formDialogVisible.value = true;
};

// 查看详情
const handleViewDetails = (record: CheckinItem) => {
  currentRecord.value = record;
  detailDialogVisible.value = true;
};

// 取消登记
const handleCancelRecord = (record: CheckinItem) => {
  currentRecord.value = record;
  cancelDialogVisible.value = true;
};

// 创建环检单
const handleCreateRepairOrder = async (record: CheckinItem) => {
  try {
    await ElMessageBox.confirm(
      t('messages.createRepairOrderConfirm', { checkinId: record.checkinId }),
      t('actions.confirm'),
      {
        confirmButtonText: t('actions.confirm'),
        cancelButtonText: t('actions.cancel'),
        type: 'info'
      }
    );

    const loadingInstance = ElLoading.service({
      lock: true,
      text: '正在创建环检单...'
    });

    try {
      const result = await createRepairOrder(record.id);
      ElMessage.success(t('messages.createRepairOrderSuccess', { repairOrderCode: result.repairOrderCode }));
      await fetchCheckinList(); // 刷新列表
    } finally {
      loadingInstance.close();
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error('创建环检单失败:', error);
      ElMessage.error('创建环检单失败');
    }
  }
};

// 表单提交
const handleFormSubmit = async (formData: CheckinFormData) => {
  try {
    if (isEdit.value && currentRecord.value) {
      // 编辑
      await updateCheckin(currentRecord.value.id, formData);
      ElMessage.success(t('messages.editSuccess'));
    } else {
      // 新增
      const result = await createCheckin(formData);
      ElMessage.success(t('messages.addSuccess', { checkinId: result.checkinId }));
    }

    formDialogVisible.value = false;
    await fetchCheckinList(); // 刷新列表

  } catch (error) {
    console.error('保存失败:', error);
    ElMessage.error('保存失败');
  }
};

// 确认取消登记
const handleCancelConfirm = async (data: { id: number; reason: string }) => {
  try {
    await cancelCheckin(data.id, data.reason);
    ElMessage.success(t('messages.cancelSuccess'));
    cancelDialogVisible.value = false;
    await fetchCheckinList(); // 刷新列表
  } catch (error) {
    console.error('取消失败:', error);
    ElMessage.error('取消失败');
  }
};

// 页码变化
const handlePageChange = (pageNum: number) => {
  pagination.current = pageNum;
  fetchCheckinList();
};

// 每页条数变化
const handlePageSizeChange = (size: number) => {
  pagination.size = size;
  pagination.current = 1;
  searchParams.pageSize = size;
  fetchCheckinList();
};

// 导出数据
const handleExport = async () => {
  try {
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '正在导出数据...'
    });

    try {
      const blob = await exportCheckinList(searchParams);

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `到店登记列表_${new Date().toISOString().slice(0, 10)}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      ElMessage.success('导出成功');
    } finally {
      loadingInstance.close();
    }

  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error('导出失败');
  }
};
</script>

<style lang="scss" scoped>
.checkin-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;

  h1 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: #303133;
  }
}

.mb-20 {
  margin-bottom: 20px;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .action-right {
    .el-button {
      margin-left: 10px;

      &:first-child {
        margin-left: 0;
      }
    }
  }
}
</style>
