<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    :close-on-click-modal="false"
    destroy-on-close
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-position="top"
      class="dialog-form-modern"
    >
      <el-form-item :label="t('store.parentStore')" prop="parentId">
        <el-input
          :value="getParentStoreDisplay()"
          :placeholder="t('store.selectParentStore')"
          readonly
          disabled
        />
      </el-form-item>

      <el-form-item :label="t('store.storeCode')" prop="storeCode">
        <el-input
          v-model="formData.storeCode"
          :placeholder="tc('inputPlaceholder')"
          :disabled="isEdit || isView"
        />
      </el-form-item>

      <el-form-item :label="t('store.storeName')" prop="storeName">
        <el-input
          v-model="formData.storeName"
          :placeholder="tc('inputPlaceholder')"
          :disabled="isView"
        />
      </el-form-item>

      <el-form-item :label="t('store.storeShortName')" prop="storeShortName">
        <el-input
          v-model="formData.storeShortName"
          :placeholder="tc('inputPlaceholder')"
          :disabled="isView"
        />
      </el-form-item>

      <el-form-item :label="t('store.storeType')" prop="storeType">
        <DictionarySelect
          v-model="formData.storeType"
          :dictionary-type="DICTIONARY_TYPES.STORE_TYPE"
          :placeholder="t('store.selectStoreType')"
          :disabled="isView"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item :label="t('store.storeProperties')" prop="storeProperties">
        <DictionaryCheckbox
          v-model="formData.storeProperties"
          :dictionary-type="DICTIONARY_TYPES.STORE_PROPERTIES"
          :disabled="isView"
        />
      </el-form-item>

      <el-form-item :label="t('store.manager')" prop="manager">
        <el-input
          v-model="formData.manager"
          :placeholder="tc('inputPlaceholder')"
          :disabled="isView"
        />
      </el-form-item>

      <el-form-item :label="t('store.contactPerson')" prop="contactPerson">
        <el-input
          v-model="formData.contactPerson"
          :placeholder="tc('inputPlaceholder')"
          :disabled="isView"
        />
      </el-form-item>

      <el-form-item :label="t('store.contactPhone')" prop="contactPhone">
        <el-input
          v-model="formData.contactPhone"
          :placeholder="tc('inputPlaceholder')"
          :disabled="isView"
        />
      </el-form-item>

      <el-form-item :label="t('store.continent')" prop="continent">
        <el-select
          v-model="formData.continent"
          :placeholder="tc('selectPlaceholder')"
          style="width: 100%"
          :disabled="isView"
          @change="handleContinentChange"
        >
          <el-option label="亚洲" value="亚洲" />
          <el-option label="欧洲" value="欧洲" />
          <el-option label="北美洲" value="北美洲" />
          <el-option label="南美洲" value="南美洲" />
          <el-option label="非洲" value="非洲" />
          <el-option label="大洋洲" value="大洋洲" />
        </el-select>
      </el-form-item>

      <el-form-item :label="t('store.city')" prop="city">
        <el-select
          v-model="formData.city"
          :placeholder="tc('selectPlaceholder')"
          style="width: 100%"
          :disabled="isView || !formData.continent"
        >
          <el-option
            v-for="city in availableCities"
            :key="city"
            :label="city"
            :value="city"
          />
        </el-select>
      </el-form-item>

      <el-form-item :label="t('store.postalCode')" prop="postalCode">
        <el-input
          v-model="formData.postalCode"
          :placeholder="tc('inputPlaceholder')"
          :disabled="isView"
        />
      </el-form-item>

      <el-form-item :label="t('store.detailAddress')" prop="detailAddress">
        <el-input
          v-model="formData.detailAddress"
          :placeholder="tc('inputPlaceholder')"
          :disabled="isView"
        />
      </el-form-item>

      <el-form-item :label="t('store.storeStatus')" prop="storeStatus">
        <DictionaryRadio
          v-model="formData.storeStatus"
          :dictionary-type="DICTIONARY_TYPES.COMMON_STATUS"
          :disabled="isView"
        />
      </el-form-item>

      <el-form-item :label="tc('remark')" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          :placeholder="tc('inputPlaceholder')"
          :disabled="isView"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer-buttons">
        <el-button @click="handleClose">
          {{ isView ? tc('close') : tc('cancel') }}
        </el-button>
        <el-button
          v-if="!isView"
          type="primary"
          @click="handleSubmit"
          :loading="loading"
        >
          {{ tc('confirm') }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import { ElForm } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import DictionaryRadio from '@/components/common/DictionaryRadio.vue';
import DictionaryCheckbox from '@/components/common/DictionaryCheckbox.vue';
import DictionarySelect from '@/components/common/DictionarySelect.vue';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import type {
  StoreItem,
  CreateStoreRequest,
  UpdateStoreRequest
} from '@/types/base/store';

interface Props {
  visible: boolean;
  isEdit: boolean;
  isView: boolean;
  storeData?: StoreItem;
  storeOptions: StoreItem[];
  loading?: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'submit', data: CreateStoreRequest | UpdateStoreRequest): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('base');

const formRef = ref<InstanceType<typeof ElForm>>();

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const getInitialFormData = (): CreateStoreRequest => ({
  storeName: '',
  storeShortName: '',
  storeCode: '',
  manager: '',
  contactPerson: '',
  contactPhone: '',
  continent: '',
  city: '',
  postalCode: '',
  detailAddress: '',
  storeType: '02010002',
  storeProperties: [],
  storeStatus: '00020001',
  remark: '',
  parentId: undefined
});

const formData = ref<CreateStoreRequest>(getInitialFormData());

const dialogTitle = computed(() => {
  if (props.isView) return tc('view') + t('store.title');
  return props.isEdit ? t('store.editStore') : t('store.addStore');
});

const formRules = computed(() => ({
  storeName: [{ required: true, message: t('store.storeNameRequired'), trigger: 'blur' }],
  storeCode: [{ required: true, message: t('store.storeCodeRequired'), trigger: 'blur' }],
  storeType: [{ required: true, message: t('store.storeTypeRequired'), trigger: 'change' }],
}));

// 洲与城市的映射关系
const continentCityMap = {
  '亚洲': ['吉隆坡', '莎阿南', '新山', '乔治市', '怡保', '古晋', '亚庇'],
  '欧洲': ['伦敦', '巴黎', '柏林', '罗马', '马德里'],
  '北美洲': ['纽约', '洛杉矶', '芝加哥', '多伦多', '温哥华'],
  '南美洲': ['圣保罗', '里约热内卢', '布宜诺斯艾利斯', '利马'],
  '非洲': ['开罗', '拉各斯', '约翰内斯堡', '卡萨布兰卡'],
  '大洋洲': ['悉尼', '墨尔本', '奥克兰', '珀斯']
};

// 可用城市列表
const availableCities = computed(() => {
  if (!formData.value.continent) return [];
  return continentCityMap[formData.value.continent as keyof typeof continentCityMap] || [];
});

// 获取父门店显示文本
const getParentStoreDisplay = () => {
  if (formData.value.storeType === '02010001') {
    return '-'; // 总部没有上级门店
  }
  return 'PERODUA总部'; // 其他门店的上级都是总部
};

// 洲变化处理
const handleContinentChange = () => {
  // 清空城市选择
  formData.value.city = '';
};

// 监听外部数据变化
watch(() => props.storeData, (newData) => {
  if (newData) {
    nextTick(() => {
      Object.assign(formData.value, newData);
    });
  } else {
    formData.value = getInitialFormData();
  }
}, { immediate: true });

// 监听弹窗显示状态
watch(() => props.visible, (visible) => {
  if (!visible) {
    // 弹窗关闭时重置表单
    formData.value = getInitialFormData();
    formRef.value?.clearValidate();
  }
});

// 监听门店类型变化，自动设置上级门店
watch(() => formData.value.storeType, (newType) => {
  if (newType === '02010001') {
    formData.value.parentId = undefined; // 总部没有上级门店
  } else {
    formData.value.parentId = '1'; // 其他门店的上级都是总部
  }
});

const handleClose = () => {
  emit('update:visible', false);
};

const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    if (props.isEdit && props.storeData) {
      const updateData: UpdateStoreRequest = {
        ...formData.value,
        id: props.storeData.id
      };
      emit('submit', updateData);
    } else {
      emit('submit', formData.value);
    }
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};
</script>

<style lang="scss" scoped>
.dialog-form-modern {
  .el-form-item {
    margin-bottom: 22px;
  }
}

.dialog-footer-buttons {
  display: flex;
  justify-content: flex-end;
  .el-button {
    margin-left: 10px;
  }
}
</style>
