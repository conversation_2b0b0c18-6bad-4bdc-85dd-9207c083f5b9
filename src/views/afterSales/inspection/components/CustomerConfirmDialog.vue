<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElDialog, ElForm, ElFormItem, ElDatePicker, ElInput, ElButton, ElDescriptions, ElDescriptionsItem } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { InspectionListItem } from '@/types/afterSales/inspection.d.ts';

interface Props {
  visible: boolean;
  recordData: InspectionListItem | null;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm', confirmTime: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.inspection');

const confirmTime = ref('');
const remarks = ref('');

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const currentRecord = computed(() => props.recordData);

// 重置表单
const resetForm = () => {
  confirmTime.value = new Date().toISOString().slice(0, 16).replace('T', ' ');
  remarks.value = '';
};

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm();
  }
});

// 确认
const handleConfirm = () => {
  if (!confirmTime.value) {
    return;
  }
  emit('confirm', confirmTime.value);
};

// 取消
const handleCancel = () => {
  dialogVisible.value = false;
};

const getRegisterTypeText = (type: string) => {
  return type === 'appointment' ? t('registerType.appointment') : t('registerType.walk_in');
};

const getServiceTypeText = (type: string) => {
  return type === 'maintenance' ? t('serviceType.maintenance') : t('serviceType.repair');
};
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="t('dialog.confirmTitle')"
    width="600px"
    @close="resetForm"
  >
    <div v-if="currentRecord">
      <!-- 检查单信息 -->
      <el-descriptions :column="2" border class="mb-20">
        <el-descriptions-item :label="t('table.inspectionNo')">
          {{ currentRecord.inspectionNo }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.licensePlateNo')">
          {{ currentRecord.licensePlateNo }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.repairmanName')">
          {{ currentRecord.repairmanName }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.repairmanPhone')">
          {{ currentRecord.repairmanPhone }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.vehicleModel')">
          {{ currentRecord.vehicleModel }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.technician')">
          {{ currentRecord.technician }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.registerType')">
          {{ getRegisterTypeText(currentRecord.registerType) }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.serviceType')">
          {{ getServiceTypeText(currentRecord.serviceType) }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 确认信息 -->
      <el-form label-position="top">
        <el-form-item :label="t('dialog.confirmTimeLabel')" required>
          <el-date-picker
            v-model="confirmTime"
            type="datetime"
            :placeholder="tc('pleaseSelect')"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item :label="t('dialog.remarksLabel')">
          <el-input
            v-model="remarks"
            type="textarea"
            :rows="3"
            :placeholder="t('dialog.remarksPlaceholder')"
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">{{ tc('cancel') }}</el-button>
        <el-button 
          type="primary" 
          @click="handleConfirm"
          :disabled="!confirmTime"
        >
          {{ tc('confirm') }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.mb-20 {
  margin-bottom: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  .el-button {
    margin-left: 10px;
  }
}
</style>
