# P01: 页面元素与数据分析清单

**目标页面**: 到店登记管理  
**提取模式**: 精密模式 (1:1提取，无设计推断)  
**生成时间**: 2025-07-31

---

## A. 数据元素清单

基于输入文件的严格提取，以下是所有用于**展示**或**输入**数据的UI组件及其包含的字段：

| UI组件类型 | 组件位置/标识 | 字段名 (原始) | 中文标签 | 备注 |
|:---|:---|:---|:---|:---|
| 表单输入框 | 搜索表单区-第一行第一列 | `checkinId` | 登记编号 | 文本输入框，支持模糊搜索，可清空 |
| 表单输入框 | 搜索表单区-第一行第二列 | `licensePlate` | 车牌号 | 文本输入框，支持模糊搜索，可清空 |
| 表单输入框 | 搜索表单区-第一行第三列 | `repairPersonName` | 送修人姓名 | 文本输入框，支持模糊搜索，可清空 |
| 表单输入框 | 搜索表单区-第一行第四列 | `repairPersonPhone` | 送修人电话 | 文本输入框，支持模糊搜索，可清空 |
| 日期范围选择器 | 搜索表单区-第二行第一列 | `createdAtStart` / `createdAtEnd` | 登记时间 | 日期范围选择器，开始/结束日期，可清空 |
| 表格列 | 数据表格-第1列 | `index` | 序号 | 自动生成序号 |
| 表格列 | 数据表格-第2列 | `checkinId` | 登记编号 | 120px宽度 |
| 表格列 | 数据表格-第3列 | `licensePlate` | 车牌号 | 120px宽度 |
| 表格列 | 数据表格-第4列 | `vin` | VIN码 | 140px宽度 |
| 表格列 | 数据表格-第5列 | `vehicleModel` | 车型 | 120px宽度 |
| 表格列 | 数据表格-第6列 | `vehicleConfiguration` | 车辆配置 | 150px宽度 |
| 表格列 | 数据表格-第7列 | `color` | 颜色 | 80px宽度 |
| 表格列 | 数据表格-第8列 | `mileage` | 里程数 | 100px宽度，格式化显示"数值 km" |
| 表格列 | 数据表格-第9列 | `repairPersonName` | 送修人姓名 | 120px宽度 |
| 表格列 | 数据表格-第10列 | `repairPersonPhone` | 送修人电话 | 120px宽度 |
| 表格列 | 数据表格-第11列 | `serviceAdvisor` | 服务顾问 | 100px宽度 |
| 表格列 | 数据表格-第12列 | `relatedRepairOrderId` | 维修工单编号 | 140px宽度，空值显示"-" |
| 表格列 | 数据表格-第13列 | `serviceType` | 服务类型 | 100px宽度 |
| 表格列 | 数据表格-第14列 | `createdAt` | 创建时间 | 120px宽度 |
| 表格列 | 数据表格-第15列 | `updatedAt` | 更新时间 | 120px宽度 |
| 分页器组件 | 分页器区域 | `page` | 当前页 | 分页页码 |
| 分页器组件 | 分页器区域 | `pageSize` | 每页条数 | 选项：[10, 20, 50, 100] |
| 分页器组件 | 分页器区域 | `total` | 总条数 | 总记录数 |
| 表单输入框 | 新增/编辑弹窗-车牌号查询区 | `licensePlateQuery` | 车牌号 | 带搜索按钮的输入框 |
| 表单输入框 | 新增/编辑弹窗-车辆信息区 | `licensePlate` | 车牌号 | 可能只读 |
| 表单输入框 | 新增/编辑弹窗-车辆信息区 | `vin` | VIN码 | 可能只读 |
| 表单输入框 | 新增/编辑弹窗-车辆信息区 | `vehicleModel` | 车型 | 可能只读 |
| 表单输入框 | 新增/编辑弹窗-车辆信息区 | `vehicleConfiguration` | 车辆配置 | 可能只读 |
| 表单输入框 | 新增/编辑弹窗-车辆信息区 | `color` | 颜色 | 可能只读 |
| 数字输入框 | 新增/编辑弹窗-车辆信息区 | `mileage` | 里程数 | 数字输入，带"km"后缀 |
| 表单输入框 | 新增/编辑弹窗-车辆信息区 | `vehicleAge` | 车龄 | 只读，自动计算 |
| 表单输入框 | 新增/编辑弹窗-客户信息区 | `repairPersonName` | 送修人姓名 | 必填 |
| 表单输入框 | 新增/编辑弹窗-客户信息区 | `repairPersonPhone` | 送修人电话 | 必填 |
| 表单输入框 | 新增/编辑弹窗-客户信息区 | `serviceAdvisor` | 服务顾问 | 必填，只读，默认当前用户 |
| 表单输入框 | 新增/编辑弹窗-服务类型区 | `serviceType` | 服务类型 | 只读，默认值"维修" |
| 多行文本框 | 新增/编辑弹窗-备注区 | `notes` | 备注信息 | 多行文本，3行，非必填 |
| 描述列表项 | 详情弹窗 | `checkinId` | 登记编号 | 必显示 |
| 描述列表项 | 详情弹窗 | `licensePlate` | 车牌号 | 必显示 |
| 描述列表项 | 详情弹窗 | `vin` | VIN码 | 必显示 |
| 描述列表项 | 详情弹窗 | `vehicleModel` | 车型 | 必显示 |
| 描述列表项 | 详情弹窗 | `vehicleConfiguration` | 车辆配置 | 必显示 |
| 描述列表项 | 详情弹窗 | `color` | 颜色 | 必显示 |
| 描述列表项 | 详情弹窗 | `mileage` | 里程数 | 显示格式"数值 km"，无数据显示"-" |
| 描述列表项 | 详情弹窗 | `vehicleAge` | 车龄 | 显示格式"数值 个月"，无数据显示"-" |
| 描述列表项 | 详情弹窗 | `repairPersonName` | 送修人姓名 | 必显示 |
| 描述列表项 | 详情弹窗 | `repairPersonPhone` | 送修人电话 | 必显示 |
| 描述列表项 | 详情弹窗 | `serviceAdvisor` | 服务顾问 | 必显示 |
| 描述列表项 | 详情弹窗 | `relatedRepairOrderId` | 关联维修工单 | 无关联显示"-" |
| 描述列表项 | 详情弹窗 | `serviceType` | 服务类型 | 必显示 |
| 描述列表项 | 详情弹窗 | `createdAt` | 创建时间 | 必显示 |
| 描述列表项 | 详情弹窗 | `updatedAt` | 更新时间 | 必显示 |
| 描述列表项 | 详情弹窗 | `notes` | 备注 | 无备注显示"-" |

**数据元素统计**: 共46个UI组件数据字段

---

## B. 用户动作清单

基于输入文件的严格提取，以下是所有可能触发后端交互的用户动作：

*   **动作名称**: 点击"搜索"按钮
    *   **输入数据**: `checkinId`, `licensePlate`, `repairPersonName`, `repairPersonPhone`, `createdAtStart`, `createdAtEnd`, `page`, `pageSize` (来自搜索表单和分页器)
    *   **期望输出/更新的UI**: 刷新"数据表格"，更新分页信息

*   **动作名称**: 点击"重置"按钮
    *   **输入数据**: 无 (清空所有搜索条件)
    *   **期望输出/更新的UI**: 清空搜索表单，重置分页到第一页，刷新"数据表格"

*   **动作名称**: 点击"新增登记"按钮
    *   **输入数据**: 无 (打开新增弹窗)
    *   **期望输出/更新的UI**: 打开新增登记弹窗

*   **动作名称**: 点击"导出"按钮
    *   **输入数据**: `checkinId`, `licensePlate`, `repairPersonName`, `repairPersonPhone`, `createdAtStart`, `createdAtEnd` (当前筛选条件)
    *   **期望输出/更新的UI**: 弹出成功提示，触发文件下载

*   **动作名称**: 点击表格行"查看详情"按钮
    *   **输入数据**: `checkinId` (来自当前行)
    *   **期望输出/更新的UI**: 打开详情查看弹窗

*   **动作名称**: 点击表格行"编辑"按钮
    *   **输入数据**: 当前行完整记录数据
    *   **期望输出/更新的UI**: 打开编辑登记弹窗

*   **动作名称**: 点击表格行"删除"按钮
    *   **输入数据**: `checkinId` (来自当前行)
    *   **期望输出/更新的UI**: 弹出确认对话框，确认后移除当前行，弹出成功提示

*   **动作名称**: 点击表格行"创建维修单"按钮
    *   **输入数据**: `checkinId` (来自当前行)
    *   **期望输出/更新的UI**: 弹出确认对话框，确认后更新当前行状态，弹出成功提示

*   **动作名称**: 分页器页码变化
    *   **输入数据**: `page` (新页码)
    *   **期望输出/更新的UI**: 刷新"数据表格"数据

*   **动作名称**: 分页器每页条数变化
    *   **输入数据**: `pageSize` (新每页条数)
    *   **期望输出/更新的UI**: 重置页码到第一页，刷新"数据表格"数据

*   **动作名称**: 新增/编辑弹窗-点击车牌号查询按钮
    *   **输入数据**: `licensePlateQuery` (车牌号查询值)
    *   **期望输出/更新的UI**: 自动填充车辆信息字段或清空并提示未找到

*   **动作名称**: 新增/编辑弹窗-点击"保存"按钮
    *   **输入数据**: `licensePlate`, `vin`, `vehicleModel`, `vehicleConfiguration`, `color`, `mileage`, `repairPersonName`, `repairPersonPhone`, `serviceAdvisor`, `serviceType`, `notes` (来自表单)
    *   **期望输出/更新的UI**: 关闭弹窗，刷新"数据表格"，弹出成功提示

*   **动作名称**: 新增/编辑弹窗-点击"取消"按钮
    *   **输入数据**: 无
    *   **期望输出/更新的UI**: 关闭弹窗

*   **动作名称**: 详情弹窗-点击"关闭"按钮
    *   **输入数据**: 无
    *   **期望输出/更新的UI**: 关闭弹窗

*   **动作名称**: 页面初始加载
    *   **输入数据**: 默认分页参数 (`page: 1`, `pageSize: 10`)
    *   **期望输出/更新的UI**: 加载并显示"数据表格"初始数据

**用户动作统计**: 共15个用户交互动作

---

## 提取总结

### 核心业务字段识别
通过精密模式提取，识别出以下核心业务数据字段：

**登记信息相关**:
- `checkinId` (登记编号)
- `createdAt` (创建时间)  
- `updatedAt` (更新时间)
- `notes` (备注信息)
- `serviceType` (服务类型)

**车辆信息相关**:
- `licensePlate` (车牌号)
- `vin` (VIN码)
- `vehicleModel` (车型)
- `vehicleConfiguration` (车辆配置)
- `color` (颜色)
- `mileage` (里程数)
- `vehicleAge` (车龄)

**客户信息相关**:
- `repairPersonName` (送修人姓名)
- `repairPersonPhone` (送修人电话)
- `serviceAdvisor` (服务顾问)

**关联信息**:
- `relatedRepairOrderId` (维修工单编号)

### 关键交互模式
- **查询模式**: 支持多条件组合搜索和分页
- **CRUD模式**: 完整的增删改查操作
- **关联模式**: 与维修工单的关联关系
- **状态模式**: 基于关联状态的操作权限控制

---

**提取完成时间**: 2025-07-31  
**下一步**: 进入步骤2 - 实体建模与关系梳理